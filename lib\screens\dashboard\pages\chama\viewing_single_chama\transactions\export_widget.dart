import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:onekitty/models/chama/chama_transactions.dart';
import 'package:onekitty/screens/dashboard/pages/chama/viewing_single_chama/transactions/statement.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/excel/excel_func.dart';
import 'package:onekitty/services/share_whatsapp_service.dart';
import 'package:open_file/open_file.dart';
import 'package:share_plus/share_plus.dart';

import '../../../../../../models/chama/get_text_transaction_request.dart';
import '../../../../../../utils/utils_exports.dart';

// ignore: must_be_immutable
class ExportChamaContentWidget extends StatefulWidget {
  final Transaction? details;
  final UserChama? chama;
  bool singleTrans;

  ExportChamaContentWidget({
    super.key,
    this.details,
    this.chama,
    required this.singleTrans,
  });

  @override
  State<ExportChamaContentWidget> createState() =>
      _ExportChamaContentWidgetState();
}

class _ExportChamaContentWidgetState extends State<ExportChamaContentWidget> {
  ChamaController chamaController = Get.put(ChamaController());
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  String shareMsg = "";

  Future<void> fetchMessage() async {
    ChamaTextTransactions request = ChamaTextTransactions(
        action: "TEXT", chamaId: chamaDataController.chama.value.chama?.id);
    final res = await chamaController.postTransactionText(requst: request);
    if (res) {
      if (mounted) {
        setState(() {
          shareMsg = chamaController.transMessage.toString();
        });
      }
    }
  }


  @override
  void initState() {
    super.initState();
    fetchMessage();
  }

  @override
  Widget build(BuildContext context) {
    final DateFormat format = DateFormat.MMMEd().add_jms();
    DateTime createdAt = widget.details?.createdAt ?? DateTime.now();
    return Container(
      width: double.maxFinite,
      padding: EdgeInsets.symmetric(
        horizontal: 20.w,
        vertical: 32.h,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 12.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomImageView(
                      imagePath: AssetUrl.imgFile,
                      height: 24.h,
                      width: 24.w,
                    ),
                    InkWell(
                      onTap: () {
                        if (widget.singleTrans) {
                          Get.to(() => SingleChamaStatementPage(
                                transaction: widget.details,
                                chama: widget.chama,
                              ));
                        } else {
                          Get.to(() => ChamaStatementPage(
                                chama: widget.chama,
                                transactions: chamaController.chamaTransactions,
                              ));
                        }
                      },
                      child: Padding(
                        padding: EdgeInsets.only(
                          left: 12.w,
                          top: 3.h,
                        ),
                        child: Text(
                          "Export to PDF",
                          style: CustomTextStyles.titleSmallGray90001,
                        ),
                      ),
                    ),
                  ],
                ),
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: CustomImageView(
                    imagePath: AssetUrl.imgIconoirCancel,
                    height: 24.h,
                    width: 24.w,
                  ),
                ),
              ],
            ),
          ),
          if (!widget.singleTrans) SizedBox(height: 23.h),
          if (!widget.singleTrans)
            Padding(
              padding: EdgeInsets.only(left: 12.w),
              child: Row(
                children: [
                  CustomImageView(
                    imagePath: AssetUrl.imgMicrosoftexcellogo,
                    height: 24.h,
                    width: 24.w,
                  ),
                  InkWell(
                    onTap: () async {
                      String filePath = await createExcel(isKitty: false);
                      showModalBottomSheet(
                          context: context,
                          builder: (BuildContext context) {
                            return Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  InkWell(
                                    onTap: () async {
                                      try {
                                        final result = await OpenFile.open(filePath);
                                        if (result.type != ResultType.done) {
                                          ScaffoldMessenger.of(context).showSnackBar(
                                            SnackBar(
                                              content: Text('Error opening file: ${result.message}'),
                                              backgroundColor: Colors.red,
                                            ),
                                          );
                                        }
                                      } catch (e) {
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          SnackBar(
                                            content: Text('Error opening file: $e'),
                                            backgroundColor: Colors.red,
                                          ),
                                        );
                                      }
                                      Navigator.pop(context);
                                    },
                                    child: const ListTile(
                                      leading: Icon(Icons.file_open),
                                      title: Text('Open File'),
                                    ),
                                  ),
                                  ListTile(
                                    leading: const Icon(Icons.share),
                                    title: const Text('Share'),
                                    onTap: () {
                                      Navigator.pop(context);
                                      Share.shareXFiles([XFile(filePath)]);
                                    },
                                  ),
                                ],
                              ),
                            );
                          });
                    },
                    child: Padding(
                      padding: EdgeInsets.only(
                        left: 12.w,
                        top: 3.h,
                      ),
                      child: Text(
                        "Export to Excel",
                        style: CustomTextStyles.titleSmallGray90001,
                      ),
                    ),
                  )
                ],
              ),
            ),
          SizedBox(height: 23.h),
          Padding(
            padding: EdgeInsets.only(left: 12.w),
            child: Row(
              children: [
                CustomImageView(
                  imagePath: AssetUrl.imgSettings,
                  height: 24.h,
                  width: 24.w,
                ),
                Padding(
                  padding: EdgeInsets.only(
                    left: 12.w,
                    top: 3.h,
                  ),
                  child: InkWell(
                    onTap: () async {
                      if (widget.singleTrans) {
                        String shareMsg =
                            "Title: ${chamaDataController.chama.value.chama?.title}\nPhone Number: ${widget.details?.phoneNumber}\nAmount: KSH ${widget.details?.amount}\nName: ${widget.details?.firstName ?? ""} ${widget.details?.secondName ?? ""}\nTransaction Code: ${widget.details?.transactionCode}\nDate: ${format.format(createdAt.toLocal())}\nChama: https://onekitty.co.ke/chama/${chamaDataController.chama.value.chama?.id}";
                        await Share.share(shareMsg,
                            subject: 'Transaction details');
                      } else {
                        try {
                          await Share.share("${chamaController.transMessage}");
                        } catch (e) {}
                      }
                    },
                    child: Text(
                      "Export to Text",
                      style: CustomTextStyles.titleSmallGray90001,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 23.h),
          Padding(
            padding: EdgeInsets.only(left: 12.w),
            child: Row(
              children: [
                CustomImageView(
                  imagePath: AssetUrl.imgVolume,
                  height: 24.h,
                  width: 24.w,
                ),
                InkWell(
                  onTap: () async {
                    Get.back();
                    if (widget.singleTrans) {
                      String shareMsg =
                          "Chama Title: ${chamaDataController.chama.value.chama?.title}\nPhone Number: ${widget.details?.phoneNumber}\nAmount: KSH ${widget.details?.amount}\nName: ${widget.details?.firstName ?? ""} ${widget.details?.secondName ?? ""}\nTransaction Code: ${widget.details?.transactionCode}\nDate: ${format.format(createdAt.toLocal())}\nChama: https://onekitty.co.ke/chama/${chamaDataController.chama.value.chama?.id}";
                      await ShareWhatsapp.share(
                        shareMsg,
                      );
                    } else {
                      try {
                        await ShareWhatsapp
                            .share("${chamaController.transMessage}");
                      } catch (e) {}
                    }
                  },
                  child: Padding(
                    padding: EdgeInsets.only(
                      left: 12.w,
                      top: 3.h,
                    ),
                    child: Text(
                      "WhatsApp message",
                      style: CustomTextStyles.titleSmallGray90001,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 5.h),
        ],
      ),
    );
  }
}
