import 'package:flutter/material.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final selectContactsRepositoryProvider = Provider(
  (ref) => SelectContactRepository(),
);

class SelectContactRepository {
  Future<List<Contact>> getContacts() async {
    List<Contact> contacts = [];
    try {
      bool hasPermission = await FlutterContacts.requestPermission();

      if (hasPermission) {
        contacts = await FlutterContacts.getContacts(withProperties: true);
      }
    } catch (e) {
      debugPrint(e.toString());
    }
    return contacts;
  }

}
