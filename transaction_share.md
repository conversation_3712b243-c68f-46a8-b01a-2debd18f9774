# Transaction Share Functionality Guide

## Overview
This guide covers the complete implementation of transaction sharing functionality in the OneKitty app, including text sharing, WhatsApp sharing, and export options.

## Dependencies Required

```yaml
dependencies:
  share_plus: ^7.2.1        # Core sharing functionality
  url_launcher: ^6.2.2      # For WhatsApp deep linking
  intl: ^0.19.0             # Date formatting
```

## Core Share Implementation

### 1. Basic Text Share (share_plus)

```dart
import 'package:share_plus/share_plus.dart';
import 'package:intl/intl.dart';

// Single transaction share
Future<void> shareTransaction(TransactionModel transaction) async {
  final DateFormat format = DateFormat.MMMEd().add_jms();
  final DataController dataController = Get.find<DataController>();
  
  String shareMsg = "Title: ${dataController.kitty.value.kitty?.title}\n"
      "Phone Number: ${transaction.phoneNumber}\n"
      "Amount: KSH ${transaction.amount}\n"
      "Name: ${transaction.firstName ?? ""} ${transaction.secondName ?? ""}\n"
      "Transaction Code: ${transaction.transactionCode}\n"
      "Date: ${format.format(transaction.createdAt!.toLocal())}\n"
      "Kitty: https://onekitty.co.ke/kitty/${transaction.kittyId}";
      
  await Share.share(shareMsg, subject: 'Transaction details');
}
```

### 2. WhatsApp Specific Share Service

```dart
// lib/services/share_whatsapp_service.dart
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';

class ShareWhatsapp {
  static share(String message, {String subject = ''}) async {
    // Define WhatsApp URIs
    final whatsappUri = Uri.parse(
        "whatsapp://send?text=${Uri.encodeComponent(message)}");
    final businessUri = Uri.parse(
        "whatsapp-business://send?text=${Uri.encodeComponent(message)}");

    // Check availability of both apps
    final whatsappAvailable = await canLaunchUrl(whatsappUri);
    final businessAvailable = await canLaunchUrl(businessUri);

    List<String> options = [];
    if (whatsappAvailable) options.add("WhatsApp");
    if (businessAvailable) options.add("WhatsApp Business");

    if (options.isEmpty) {
      // Fallback if neither app is installed
      await Share.share(message, subject: subject);
      return;
    }

    try {
      await launchUrl(whatsappUri);
    } catch (e) {
      // Fallback if launching fails
      await Share.share(message, subject: 'Transaction details');
    }
  }
}
```

### 3. Transaction Dialog with Share Button

```dart
// Transaction dialog implementation
showTransactionDialog({
  required BuildContext context,
  required TransactionModel details,
  bool singleTrans = true,
  int? eventId,
}) {
  return showAnimatedDialog(
    barrierDismissible: true,
    animationType: DialogTransitionType.sizeFade,
    curve: Curves.fastOutSlowIn,
    duration: const Duration(milliseconds: 900),
    context: context,
    builder: (BuildContext context) {
      final DateFormat format = DateFormat.MMMEd().add_jms();
      DateTime createdAt = details.createdAt!.toLocal();
      final DataController dataController = Get.find<DataController>();
      
      return Dialog(
        child: SizedBox(
          width: SizeConfig.screenWidth * .8,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 10.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                // Transaction details UI...
                
                // Share Button
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).canvasColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20.sp),
                      side: BorderSide(
                        width: 2.sp,
                        color: Theme.of(context).primaryColor
                      ),
                    ),
                  ),
                  onPressed: () async {
                    String shareMsg = "Title: ${dataController.kitty.value.kitty?.title}\n"
                        "Phone Number: ${details.phoneNumber}\n"
                        "Amount: KSH ${details.amount}\n"
                        "Name: ${details.firstName ?? ""} ${details.secondName ?? ""}\n"
                        "Transaction Code: ${details.transactionCode}\n"
                        "Date: ${format.format(createdAt.toLocal())}\n"
                        "Kitty: https://onekitty.co.ke/kitty/${details.kittyId}";
                    await Share.share(shareMsg, subject: 'Transaction details');
                  },
                  child: const Text(
                    "Share",
                    style: TextStyle(color: Colors.black),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    },
  );
}
```

## Export Widget with Multiple Share Options

### Export Bottom Sheet Implementation

```dart
// Export widget with multiple sharing options
class ExportContentWidget2 extends StatefulWidget {
  final TransactionModel? details;
  final bool singleTrans;
  final int? eventId;

  const ExportContentWidget2({
    super.key,
    this.details,
    required this.singleTrans,
    this.eventId,
  });

  @override
  State<ExportContentWidget2> createState() => _ExportContentWidget2State();
}

class _ExportContentWidget2State extends State<ExportContentWidget2> {
  KittyController kittyController = Get.put(KittyController());
  final DataController dataController = Get.put(DataController());
  String shareMsg = "";

  @override
  Widget build(BuildContext context) {
    final DateFormat format = DateFormat.MMMEd().add_jms();
    DateTime createdAt = widget.details?.createdAt?.toLocal() ?? DateTime.now();
    
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Export to Text Option
          ListTile(
            leading: const Icon(Icons.text_fields),
            title: const Text("Export to Text"),
            onTap: () async {
              if (widget.singleTrans) {
                String shareMsg = "Title: ${dataController.kitty.value.kitty?.title}\n"
                    "Phone Number: ${widget.details?.phoneNumber}\n"
                    "Amount: KSH ${widget.details?.amount}\n"
                    "Name: ${widget.details?.firstName ?? ""} ${widget.details?.secondName ?? ""}\n"
                    "Transaction Code: ${widget.details?.transactionCode ?? ''}\n"
                    "Date: ${format.format(createdAt.toLocal())}\n"
                    "Kitty: https://onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.id}";
                await Share.share(shareMsg, subject: 'Transaction details');
              } else {
                await Share.share("${kittyController.textmessage}");
              }
            },
          ),
          
          // WhatsApp Share Option
          ListTile(
            leading: const Icon(Icons.message),
            title: const Text("WhatsApp message"),
            onTap: () async {
              Get.back();
              if (widget.singleTrans) {
                String shareMsg = "Kitty Title: ${dataController.kitty.value.kitty?.title ?? ''}\n"
                    "Phone Number: ${widget.details?.phoneNumber}\n"
                    "Amount: KSH ${widget.details?.amount}\n"
                    "Name: ${widget.details?.firstName ?? ""} ${widget.details?.secondName ?? ""}\n"
                    "Transaction Code: ${widget.details?.transactionCode ?? ''}\n"
                    "Date: ${format.format(createdAt.toLocal())}\n"
                    "Kitty: https://onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.id}";
                ShareWhatsapp.share(shareMsg);
              } else {
                ShareWhatsapp.share("${kittyController.textmessage}");
              }
            },
          ),
        ],
      ),
    );
  }
}
```

## Usage Examples

### 1. Share from Transaction Item

```dart
// In transaction item widget
onTap: () {
  showTransactionDialog(
    context: context,
    details: item,
    eventId: eventId,
  );
}
```

### 2. Share from Export Button

```dart
// Export button in floating action button
FloatingActionButton(
  onPressed: () {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return ExportContentWidget2(
          eventId: widget.eventId,
          singleTrans: singleTrans,
        );
      },
    );
  },
  child: Icon(Icons.share),
)
```

### 3. Direct Share Function

```dart
// Direct share without dialog
Future<void> shareTransactionDirect(TransactionModel transaction) async {
  final format = DateFormat.MMMEd().add_jms();
  final dataController = Get.find<DataController>();
  
  String message = "Title: ${dataController.kitty.value.kitty?.title}\n"
      "Phone Number: ${transaction.phoneNumber}\n"
      "Amount: KSH ${transaction.amount}\n"
      "Name: ${transaction.firstName ?? ""} ${transaction.secondName ?? ""}\n"
      "Transaction Code: ${transaction.transactionCode}\n"
      "Date: ${format.format(transaction.createdAt!.toLocal())}\n"
      "Kitty: https://onekitty.co.ke/kitty/${transaction.kittyId}";
      
  await Share.share(message, subject: 'Transaction details');
}
```

## Key Features

1. **Multiple Share Options**: Text share, WhatsApp share
2. **Formatted Messages**: Structured transaction details
3. **Fallback Handling**: Graceful degradation when WhatsApp unavailable
4. **Deep Linking**: Direct WhatsApp integration
5. **Contextual Sharing**: Different formats for single vs bulk transactions
6. **Error Handling**: Try-catch blocks for robust sharing

## Message Format Template

```
Title: [Kitty/Event Title]
Phone Number: [User Phone]
Amount: KSH [Amount]
Name: [First Name] [Last Name]
Transaction Code: [Transaction Code]
Date: [Formatted Date]
Kitty: https://onekitty.co.ke/kitty/[Kitty ID]
```

This implementation provides a complete, robust transaction sharing system with multiple sharing options and proper error handling.
