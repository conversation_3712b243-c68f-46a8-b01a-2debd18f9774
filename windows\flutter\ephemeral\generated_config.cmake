# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\sdksss\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "E:\\onekitty Projects\\onekitty" PROJECT_DIR)

set(FLUTTER_VERSION "4.12.0+40" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 4 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 12 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 40 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\sdksss\\flutter"
  "PROJECT_DIR=E:\\onekitty Projects\\onekitty"
  "FLUTTER_ROOT=D:\\sdksss\\flutter"
  "FLUTTER_EPHEMERAL_DIR=E:\\onekitty Projects\\onekitty\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=E:\\onekitty Projects\\onekitty"
  "FLUTTER_TARGET=E:\\onekitty Projects\\onekitty\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNA==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049NmZiYTI0NDdlOQ==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049OGNkMTllNTA5ZA==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=E:\\onekitty Projects\\onekitty\\.dart_tool\\package_config.json"
)
