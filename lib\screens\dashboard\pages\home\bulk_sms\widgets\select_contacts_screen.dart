import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/bulksms_controller.dart';
import 'package:onekitty/models/bulk_sms/sms_groups.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/widgets/contacts_controller.dart';
import 'package:flutter_contacts/contact.dart';

import '../../../../../../helpers/show_toast.dart';
import '../create_msg_group.dart';

class SelectContactsScreen extends ConsumerStatefulWidget {
  final bool isGroup;
  const SelectContactsScreen({super.key, this.isGroup = false});

  @override
  _SelectContactsScreenState createState() => _SelectContactsScreenState();
}

class _SelectContactsScreenState extends ConsumerState<SelectContactsScreen> {
  Set<Contact> selectedContacts = <Contact>{};
  final controller = Get.put(CreateMsgController());

  void selectContact(Contact selectedContact, BuildContext context) {
    final contactList = ref
        .watch(selectContactControllerProvider.notifier)
        .getSelectedContacts();
    if (!contactList.contains(selectedContact)) {
      selectedContact.name.prefix = "MEMBER";
      ref
          .read(selectContactControllerProvider.notifier)
          .selectContact(selectedContact, context);
      if (widget.isGroup) {
        controller.groupMembers.add(SmsGroupMember(
            phoneNumber: selectedContact.phones.first.number,
            firstName: selectedContact.name.first,
            secondName: selectedContact.name.last));
      } else {
        selectedContacts.add(selectedContact);
        Get.put(BulkSMSController()).selectedContacts.add(SelectedContact(
              contact: selectedContact,
              type: 'contact',
            ));
      }
      setState(() {});
    } else {
      ToastUtils.showInfoToast(context, "Contact Already Selected",
          "Check"); // Show SnackBar if contact is already selected
    }
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var isFrom = Get.arguments;
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select contact'),
        actions: [
          IconButton(
            onPressed: () {
              showSearch(
                context: context,
                delegate: ContactSearchDelegate(
                  ref,
                  (contact, context) {
                    selectContact(contact, context);
                    setState(
                        () {}); // Trigger UI update after contact selection
                  },
                ),
              );
            },
            icon: const Icon(
              Icons.search,
            ),
          ),
          IconButton(
            onPressed: () {},
            icon: const Icon(
              Icons.more_vert,
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: ref
                  .watch(selectContactControllerProvider.notifier)
                  .getSelectedContacts()
                  .length,
              itemBuilder: (context, index) {
                final selectedContact = ref
                    .watch(selectContactControllerProvider.notifier)
                    .getSelectedContacts()[index];

                return Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    children: [
                      const SizedBox(width: 8), // Adjust spacing as needed
                      Column(
                        children: [
                          CircleAvatar(
                            backgroundImage: selectedContact.photo != null
                                ? MemoryImage(selectedContact.photo!)
                                : null,
                            radius: 20,
                            child: Text(
                              selectedContact.displayName.isNotEmpty
                                  ? selectedContact.displayName[0]
                                  : '', // Provide a default value like 'No Name'
                            ),
                          ),
                          const SizedBox(height: 2), // Adjust spacing as needed
                          Text(
                            selectedContact.displayName.isNotEmpty
                                ? selectedContact.displayName
                                : selectedContact.phones.first.normalizedNumber,
                            textAlign: TextAlign.center,
                            style: const TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                      InkWell(
                        onTap: () {
                          if (widget.isGroup) {
                            controller.groupMembers
                                .map((e) => e.phoneNumber)
                                .toList()
                                .removeWhere(
                                  (e) =>
                                      e == selectedContact.phones.first.number,
                                
                                  // {
                                  //   "phone_number":
                                  //       selectedContact.phones.first.number,
                                  //   "first_name": selectedContact.name.first,
                                  //   "second_name": selectedContact.name.last
                                  // }
                                );
                          }
                          ref
                              .read(selectContactControllerProvider.notifier)
                              .removeSelectedContact(selectedContact);
                          setState(() {});
                        },
                        child: const Icon(Icons.cancel), // Cancel icon
                      ),
                    ],
                  ),
                );
              },
            ),
          ),

          // Divider to separate selected contacts from other content
          const Divider(
            color: Colors.grey,
            height: 1,
          ),

          Expanded(
            child: ref.watch(getContactsProvider).when(
                  data: (contactList) => ListView.builder(
                    itemCount: contactList.length,
                    itemBuilder: (context, index) {
                      final contact = contactList[index];
                      final selectedContact = ref
                          .watch(selectContactControllerProvider.notifier)
                          .getSelectedContacts();

                      final isSelected = selectedContact.contains(contact);
                      return InkWell(
                        onTap: () => selectContact(contact, context),
                        child: Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: ListTile(
                            title: Row(
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      contact.displayName,
                                      style: const TextStyle(
                                        fontSize: 18,
                                      ),
                                    ),
                                    Text(
                                      contact.phones.isNotEmpty
                                          ? contact.phones.first.number
                                          : 'No Number',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey,
                                      ),
                                    ),
                                  ],
                                ),
                                if (isSelected) // Check if contact is selected
                                  const Icon(Icons.check,
                                      color: Colors
                                          .green), // Show tick icon if selected
                                SizedBox(width: isSelected ? 8 : 0),
                              ],
                            ),
                            leading: contact.photo == null
                                ? null
                                : CircleAvatar(
                                    backgroundImage:
                                        MemoryImage(contact.photo!),
                                    radius: 30,
                                  ),
                          ),
                        ),
                      );
                    },
                  ),
                  error: (err, trace) => Text(err.toString()),
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          isFrom == true ? Get.back() : Get.back();
        },
        child: const Icon(Icons.check),
      ),
    );
  }
}

class ContactSearchDelegate extends SearchDelegate<Contact> {
  final WidgetRef ref;
  final Function(Contact, BuildContext) selectContactCallback;

  ContactSearchDelegate(this.ref, this.selectContactCallback);

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, Contact());
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return buildSearchResults(context);
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return buildSearchResults(context);
  }

  Widget buildSearchResults(BuildContext context) {
    final contactList = ref.watch(getContactsProvider);

    return contactList.when(
      data: (contacts) {
        final filteredContacts = contacts
            .where((contact) =>
                contact.displayName.toLowerCase().contains(query.toLowerCase()))
            .toList();

        return ListView.builder(
          itemCount: filteredContacts.length,
          itemBuilder: (context, index) {
            final contact = filteredContacts[index];
            return InkWell(
              onTap: () => selectContactCallback(contact, context),
              child: ListTile(
                title: Text(contact.displayName),
                onTap: () {
                  close(context, contact);
                  selectContactCallback(contact, context);
                },
              ),
            );
          },
        );
      },
      error: (err, trace) => Text(err.toString()),
      loading: () => const Center(child: CircularProgressIndicator()),
    );
  }
}
