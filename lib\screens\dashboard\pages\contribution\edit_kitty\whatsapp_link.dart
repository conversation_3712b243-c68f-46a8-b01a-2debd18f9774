import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/custom_button.dart';

class WhatsAppEditLink extends StatefulWidget {
  final int? kittyId;
  const WhatsAppEditLink({super.key, this.kittyId});

  @override
  State<WhatsAppEditLink> createState() => _WhatsAppEditLinkState();
}

class _WhatsAppEditLinkState extends State<WhatsAppEditLink> {
  final linkController = TextEditingController();
  final formKey = GlobalKey<FormState>();
  final kittyController = Get.put(KittyController());
  final dataController = Get.put(DataController());
  final box = GetStorage();
  bool isLoading = false;
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Container(
          margin: const EdgeInsets.all(20),
          child: Form(
            key: formKey,
            child: Column(
              children: [
                Text(
                  "Connect WhatsApp Group",
                  style:
                      context.titleText?.copyWith(fontWeight: FontWeight.bold),
                ),
                Text(dataController.kitty.value.kitty?.title ?? ""),
                const SizedBox(
                  height: 30,
                ),
                Align(
                  alignment: Alignment.topLeft,
                  child: Text(
                    "Whatsapp group link",
                    style: context.titleText,
                  ),
                ),
                SizedBox(
                  height: 10.h,
                ),
                CustomTextField(
                  labelText: box.read("whatsapplink") == null
                      ? "Enter whatsapp link"
                      : "${box.read("whatsapplink")}",
                  hintText: "Enter Whatsapp link",
                  //hintText: "https://chat.whatsapp.com/kqkeodbbfjNLKlqwo",
                  controller: linkController,
                  validator: (p0) {
                    if (p0!.isEmpty) {
                      return "Provide a link";
                    } else if (!RegExp(
                            r'^https:\/\/chat\.whatsapp\.com\/[A-Za-z0-9_-]{22}$')
                        .hasMatch(p0)) {
                      return "Enter a valid WhatsApp link";
                    }
                    return null;
                  },
                ),
                const SizedBox(
                  height: 20,
                ),
                SizedBox(
                  height: 50,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      OutlinedButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 12.0),
                            child: Text(
                              "Back",
                              style: context.dividerTextLarge
                                  ?.copyWith(color: AppColors.primary),
                            ),
                          )),
                      Obx(() => CustomKtButton(
                            width: 100.w,
                            isLoading: kittyController.isLinkloading.isTrue,
                            onPress: () async {
                              if (formKey.currentState!.validate()) {
                                if (await kittyController.joinGroup(
                                  context: context,
                                  id: widget.kittyId ?? dataController.kitty.value.kitty?.id ?? 0,
                                  link: linkController.text.trim(),
                                )) {
                                  // Refresh WhatsApp group data before closing
                                 
                                  // Pop the current screen to go back to viewing page
                                  Navigator.pop(context);
                                }
                              }
                            },
                            btnText: "Submit",
                          ))
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
