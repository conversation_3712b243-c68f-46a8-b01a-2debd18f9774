import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/models/events/categories_model.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/custom_logger.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/utils/show_snackbar.dart';
import 'package:dio/dio.dart' as dios;
import '/models/events/media_models.dart';
import 'events_controller.dart';
import 'view_single_event.dart';

class EditEventController extends GetxController implements GetxService {
  Rx<Event> event = Rx<Event>(Event(
    id: 0,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    deletedAt: null,
    title: '',
    username: '',
    description: '',
    email: '',
    venue: '',
    latitude: 0.0,
    longitude: 0.0,
    locationTip: '',
    categoryId: 0,
    category: CategoriesModel(
        id: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        deletedAt: null,
        title: "",
        description: "",
        avatar: ""),
    tickets: [],
    eventMedia: [],
    socialAccounts: [],
    startDate: DateTime.now(),
    endDate: DateTime.now(),
  ));
  RxBool isloading = false.obs;
  RxBool isFetchingMedia = false.obs;
  var logger = Logger(filter: CustomLogFilter());
  final HttpService apiProvider = Get.find();

  RxDouble long = 0.0.obs, lat = 0.0.obs;
  RxList<CategoriesModel> categories = <CategoriesModel>[].obs;
  RxBool isLoadingCategories = false.obs;
  RxBool isUploading = false.obs;
  RxBool isEditing = false.obs;
  RxBool hasSignatoryTransactions = false.obs;

  RxInt category = 0.obs;
  RxList<EventMedia> eventMedia = <EventMedia>[].obs;
  final Rx<CategoriesModel?> eventcategory = Rx<CategoriesModel?>(null);

  Future<void> getCategories() async {
    try {
      isLoadingCategories(true);
      final httpService = HttpService();
      final dio = httpService.initializeDio();
      final response = await dio.get(ApiUrls.GETCATEGORIES);

      if (response.data != null) {
        final _returneddata = response.data['data']['categories'] as List;
        categories = _returneddata
            .map((item) {
              return CategoriesModel.fromJson(item);
            })
            .toList()
            .obs;
      } else {
        logger.v('No data or unexpected response structure');
      }
      Future.microtask(() => isLoadingCategories(false));
    } catch (e) {
      logger.e('Error fetching events: $e');
    } finally {
      isLoadingCategories(false);
    }
  }

  Future<bool> fetchEventDetailbyUsername(String username,
      {bool? isOrganizer}) async {
    try {
      isloading(true);

      final response = await apiProvider.request(
          method: Method.GET, url: "${ApiUrls.GETEVENTBYUSERNAME}$username");
      if (response.data != null) {
        final _returneddata = response.data['data'] as Map<String, dynamic>;
        final myEvent = MyEventsModel.fromJson(_returneddata);
        event.value = myEvent.event;
        Get.put(ViewSingleEventController()).event.value = event.value;
        lat = event.value.latitude.obs;
        long = event.value.longitude.obs;
        eventMedia.value = event.value.eventMedia ?? [];
        eventcategory(event.value.category);
        update();

        isloading(false);
        return true;
      } else {
        logger.v('No data or unexpected response structure');
      }

      isloading(false);
      return false;
    } catch (e) {
      logger.e('Error fetching events: $e');
      Future.delayed(Duration.zero, () {
        isloading(false);
      });
      return false;
    }
  }

  Future<void> fetchEventDetail(int id, {bool? isOrganizer}) async {
    try {
      isloading(true);

      final response = await apiProvider.request(
          method: Method.GET, url: "${ApiUrls.GETEVENTBYID}$id");
      if (response.data != null) {
        final _returneddata = response.data['data'] as Map<String, dynamic>;
        final myEvent = MyEventsModel.fromJson(_returneddata);
        event.value = myEvent.event;
        lat = event.value.latitude.obs;
        long = event.value.longitude.obs;
        eventMedia.value = event.value.eventMedia ?? [];
        eventcategory(event.value.category);
        if (response.data['data']['has_signatory_transactions'] != null) {
          hasSignatoryTransactions(
              response.data['data']['has_signatory_transactions']);
        }
// THIS SHOULD BE ABLE TO UPDATE THE REFRESHED EVENT TO THE MAIN LIST
        final eventsController = Get.find<Eventcontroller>();
        Get.find<ViewSingleEventController>().event.value = event.value;
        if (isOrganizer ?? false) {
          int index =
              eventsController.userEvents.indexWhere((e) => e.event.id == id);
          if (index != -1) {
            Get.find<Eventcontroller>().userEvents[index] = myEvent;
          }
          update();
        }
      } else {
        logger.v('No data or unexpected response structure');
      }
      isloading(false);
    } catch (e) {
      logger.e('Error fetching events: $e');
      Future.delayed(Duration.zero, () {
        isloading(false);
      });
    }
  }

  Future<Map<String, dynamic>?> uploadFile(String path) async {
    isUploading(true);
    try {
      var data = dios.FormData.fromMap({
        'file': await dios.MultipartFile.fromFile(path,
            filename:
                "${DateTime.now().millisecondsSinceEpoch}_${path.split(RegExp(r'[/\\]')).last}"),
        'bucket': 'onekitty'
      });
      // Use the existing HttpService instance
      final response = await apiProvider.request(
        url: "${HttpService.baseUrl!}${ApiUrls.UPLOADFILE}",
        method: Method.POST,
        formdata: data, // Pass the FormData
      );
      isUploading(false);
      if (response.data['status'] ?? false) {
        return response.data['data'];
      } else {
        return null;
      }
    } catch (e) {
      isUploading(false);
      logger.e('Error uploading file: $e');
      isLoadingCategories(false);
      return null;
    }
  }

  Future<bool> updateEventMedia(int id,
      {bool? delete, int? pos, String? mediaUrl}) async {
    isUploading(true);
    try {
      Map<String, dynamic>? data;
      if (delete ?? false) {
        var res = await apiProvider.request(
            url: "${ApiUrls.DELETEMEDIA}${eventMedia[pos ?? 0].id}",
            method: Method.DELETE);
        if (res.data["status"] ?? false) {
          eventMedia.removeAt(pos ?? 0);
          isUploading(false);
          return true;
        } else {
          logger.e('Error deleting media: ${res.data["message"]}');
          isUploading(false);
          return false;
        }
      } else {
        if (event.value.socialAccounts!.isEmpty) {
          data = {
            "event_id": id,
            "media": [
              {"media": mediaUrl, "type": 'image'}
            ],
            "SocialAccounts": {
              "facebook": "",
              "tiktok": "",
              "Instagram": "",
              "youtube": "",
              "twitter": "",
              "hearthis": "",
              "website": ""
            }
          };
        } else {
          data = {
            "event_id": id,
            "media": [
              {"media": mediaUrl, "type": 'image'}
            ],
            "SocialAccounts": {
              "facebook": event.value.socialAccounts?[0].facebook,
              "tiktok": event.value.socialAccounts?[0].tiktok,
              "Instagram": event.value.socialAccounts?[0].instagram,
              "youtube": event.value.socialAccounts?[0].youtube,
              "twitter": event.value.socialAccounts?[0].twitter,
              "hearthis": event.value.socialAccounts?[0].hearthis,
              "website": event.value.socialAccounts?[0].website
            }
          };
        }

        var res = await apiProvider.request(
            url: ApiUrls.ADDSOCIALMEDIA, method: Method.POST, params: data);
        if (res.data["status"] ?? false) {
          isUploading(false);
          return true;
        } else {
          logger.e('Error adding media: ${res.data["message"]}');
          isUploading(false);
          return false;
        }
      }
    } catch (e) {
      logger.e('Exception in updateEventMedia: $e');
      isUploading(false);
      return false;
    }
  }

  Future<void> editEvent({
    required data,
    required BuildContext context,
  }) async {
    try {
      isEditing(true);
      // print(json.encode(data).toString());
      // isEditing(false);
      var res = await apiProvider.request(
          url: ApiUrls.EDITEVENT, method: Method.PUT, params: data);
      if (res.data["status"] ?? false) {
        isEditing(false);
        showSnackbar(context: context, label: 'Suceess', color: primaryColor);
      } else {
        isEditing(false);
        showSnackbar(context: context, label: 'error: ${res.data["message"]}');
      }
    } catch (e) {
      isEditing(false);
      // showSnackbar(
      //     context: context, label: 'error: $e', color: Colors.deepOrange);
      logger.e(e);
    }
  }
}

// Note: Consider refactoring DateTime conversions to a centralized utility
// to ensure .toLocal() and .toUtc() are applied consistently across the app.
