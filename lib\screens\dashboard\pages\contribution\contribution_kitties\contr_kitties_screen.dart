import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/models/auth/user_model.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/create_or_contri_kitty_screen.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../../../../controllers/user_ktty_controller.dart';
import '../../../../../utils/utils_exports.dart';
import 'widgets/single_kitty_card.dart';

// ignore_for_file: must_be_immutable
class MyKittiesScreen extends StatefulWidget {
  const MyKittiesScreen({super.key});

  @override
  State<MyKittiesScreen> createState() => _MyKittiesScreenState();
}

class _MyKittiesScreenState extends State<MyKittiesScreen> {
  TextEditingController searchController = TextEditingController();

  UserModelLatest user = UserModelLatest();

  UserKittyController userController = Get.find<UserKittyController>();

  String greeting = getGreeting();

  DateTime tagetDate = DateTime.now().add(const Duration(days: 1));
  ContributeController singleKitty = Get.put(ContributeController());

  // Debounce for search to avoid too many API calls
  Timer? _debounce;

  // Track if initial data has been loaded
  bool initialLoadDone = false;

  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  void _onRefresh() async {
    searchController.clear();
    // Reset pagination and clear list before fetching page 0
    userController.reset();
    await userController.getUserkitties(page: 0);
    _refreshController.refreshCompleted();
  }

  @override
  void initState() {
    super.initState();
    // Trigger initial load if kitties list is empty
    if (userController.kitties.isEmpty &&
        !userController.kittiesLoading.value) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        userController.getUserkitties(page: 0);
      });
    }
  }

  @override
  void dispose() {
    searchController.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  // Handle search with API
  void _handleSearch(String query) {
    if (_debounce?.isActive ?? false) _debounce?.cancel();

    _debounce = Timer(const Duration(milliseconds: 500), () {
      userController.searchKitties(query);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBarWithImage(context),
      body: SmartRefresher(
        onRefresh: _onRefresh,
        controller: _refreshController,
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            SizedBox(height: 10.h),
            // Header section with "My Kitties" title and search
            // Obx(
            //   () =>
            //    userController.kitties.isEmpty
            //   //  && !userController.kittiesLoading.value
            //       ? const SizedBox()
            // :
            Column(children: [
              Padding(
                padding: EdgeInsets.only(left: 20.w, right: 20.w),
                child: _buildRow(context),
              ),
              SizedBox(height: 24.h),
              Padding(
                padding: EdgeInsets.only(left: 20.w, right: 20.w),
                child: TextFormField(
                  autofocus: false,
                  controller: searchController,
                  decoration: InputDecoration(
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: Obx(() => userController.searchLoading.value
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: Padding(
                              padding: EdgeInsets.all(12.0),
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          )
                        : (searchController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () {
                                  searchController.clear();
                                  userController.searchKitties('');
                                })
                            : const SizedBox())),
                    hintText: "Search for a Kitty",
                  ),
                  onChanged: _handleSearch,
                ),
              )
            ]),
            // ),

            // Main content area with kitties list or loading state
            Expanded(
              child: Obx(() {
                // Handle search loading state
                if (userController.searchLoading.value) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SpinKitDualRing(
                          color: ColorUtil.blueColor,
                          lineWidth: 4.sp,
                          size: 40.0.sp,
                        ),
                        const Text(
                          "Searching kitties...",
                          style: TextStyle(
                            color: Colors.white,
                          ),
                        )
                      ],
                    ),
                  );
                }

                // Handle prefetching indicator (optional, shows loading state during prefetch)
                Widget? prefetchIndicator;
                if (userController.isPrefetching.value &&
                    !userController.loadingMore.value) {
                  prefetchIndicator = Positioned(
                    bottom: 10,
                    right: 10,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.black54,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          SizedBox(
                            width: 15,
                            height: 15,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          ),
                          SizedBox(width: 8),
                          Text(
                            "Prefetching...",
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                // Handle main loading state
                if (userController.kittiesLoading.value &&
                    userController.kitties.isEmpty) {
                  return SizedBox(
                    height: SizeConfig.screenHeight * .33,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SpinKitDualRing(
                            color: ColorUtil.blueColor,
                            lineWidth: 4.sp,
                            size: 40.0.sp,
                          ),
                          const Text(
                            "loading kitties...",
                            style: TextStyle(
                              color: Colors.white,
                            ),
                          )
                        ],
                      ),
                    ),
                  );
                }

                // Handle empty state - no kitties at all
                if (userController.kitties.isEmpty &&
                    !userController.kittiesLoading.value) {
                  if (userController.searchQuery.isNotEmpty) {
                    // No kitties found for search query
                    return Center(
                      child: Padding(
                        padding: EdgeInsets.only(top: 15.h),
                        child: Text(
                          "No kitties found for '${userController.searchQuery.value}'",
                          style: const TextStyle(
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                    );
                  }
                  // No kitties at all - show create kitty page
                  return const CrtContributionKittyPage();
                }

                // Show kitties list
                return Stack(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20.0, vertical: 20),
                      child: Column(
                        children: [
                          // List of kitties
                          Expanded(
                            child: ListView.separated(
                              controller: userController.scrollController,
                              physics: const BouncingScrollPhysics(),
                              keyboardDismissBehavior:
                                  ScrollViewKeyboardDismissBehavior.onDrag,
                              separatorBuilder: (context, index) =>
                                  SizedBox(height: 24.h),
                              itemCount: userController.kitties.length +
                                  ((userController.loadingMore.isTrue ||
                                          (userController.isLastPage.isTrue &&
                                              !userController
                                                  .loadingMore.value &&
                                              userController
                                                  .searchQuery.isEmpty))
                                      ? 1
                                      : 0),
                              itemBuilder: (context, index) {
                                // If this is the last item and we need to show loader or end message
                                if (index == userController.kitties.length) {
                                  // Show loading indicator
                                  if (userController.loadingMore.isTrue) {
                                    return const Padding(
                                      padding:
                                          EdgeInsets.symmetric(vertical: 16.0),
                                      child: Column(
                                        children: [
                                          SpinKitDualRing(
                                            color: ColorUtil.blueColor,
                                            lineWidth: 4,
                                            size: 40.0,
                                          ),
                                          Text(
                                            "loading more kitties...",
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontStyle: FontStyle.italic,
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  }
                                  // Show end of list indicator
                                  if (userController.isLastPage.isTrue &&
                                      !userController.loadingMore.value &&
                                      userController.searchQuery.isEmpty) {
                                    return const Padding(
                                      padding:
                                          EdgeInsets.symmetric(vertical: 16.0),
                                      child: Text(
                                        "No more kitties to load",
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          fontStyle: FontStyle.italic,
                                        ),
                                      ),
                                    );
                                  }
                                  // Fallback empty widget in case conditions don't match
                                  return const SizedBox();
                                }

                                // Regular kitty item
                                final kitty = userController.kitties[index];
                                return ContributionKittyWidget(kitty: kitty);
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Show prefetch indicator if needed
                    if (prefetchIndicator != null) prefetchIndicator,
                  ],
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildCreateAKittyButton(BuildContext context) {
    return CustomElevatedButton(
      isDisabled: false,
      onPressed: () {
        Get.toNamed(NavRoutes.createkittyScreen);
      },
      width: 150.w,
      height: 40.h,
      text: "Create a Kitty",
      buttonTextStyle: TextStyle(
        fontSize: 12.h,
        color: Colors.white,
        fontWeight: FontWeight.bold,
      ),
      leftIcon: Container(
        margin: EdgeInsets.only(right: 8.w),
        child: CustomImageView(
          imagePath: AssetUrl.imgPlus,
          height: 18.h,
          width: 18.w,
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildRow(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(right: 2.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: EdgeInsets.only(
              top: 8.h,
              bottom: 8.h,
            ),
            child: Text(
              "My Kitties",
              style: theme.textTheme.titleLarge,
              // ignore: deprecated_member_use
              textScaleFactor: 0.8,
            ),
          ),
          _buildCreateAKittyButton(context),
        ],
      ),
    );
  }
}
