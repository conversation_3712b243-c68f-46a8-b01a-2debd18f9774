import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// import 'package:google_ml_kit/google_ml_kit.dart';
import 'package:camera/camera.dart';
// import 'package:google_ml_kit/google_ml_kit.dart';
import 'package:permission_handler/permission_handler.dart';
import '../views/review_subit_page.dart';
import '../widgets/clay_progress_bar.dart';
import '../controllers/kyc_controller.dart';

class CaptureSelfiePage extends StatefulWidget {
  const CaptureSelfiePage({super.key});

  @override
  State<CaptureSelfiePage> createState() => _CaptureSelfiPageState();
}

class _CaptureSelfiPageState extends State<CaptureSelfiePage> {
  final KYCController controller = Get.find<KYCController>();
  // final FaceDetector faceDetector = GoogleMlKit.vision.faceDetector();
  CameraController? cameraController;
  bool isCameraInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeCamera();
  }

  @override
  void dispose() {
    cameraController?.dispose();
    // faceDetector.close();
    super.dispose();
  }

  Future<void> _initializeCamera() async {
    final status = await Permission.camera.status;
    if (status.isDenied) {
      final result = await Permission.camera.request();
      if (result.isDenied) {
        Get.snackbar(
          'Permission Denied',
          'Camera permission is required to capture selfie',
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.red[200],
        );
        return;
      }
    }

    final cameras = await availableCameras();
    if (cameras.isEmpty) return;

    // Use front camera for selfie
    final frontCamera = cameras.firstWhere(
      (camera) => camera.lensDirection == CameraLensDirection.front,
      orElse: () => cameras.first,
    );

    cameraController = CameraController(
      frontCamera,
      ResolutionPreset.high,
      enableAudio: false,
    );

    try {
      await cameraController!.initialize();
      if (mounted) {
        setState(() {
          isCameraInitialized = true;
        });
      }
    } catch (e) {
      Get.snackbar(
        'Camera Error',
        'Failed to initialize camera: ',
        snackPosition: SnackPosition.bottom,
      );
    }
  }

  Future<void> _captureImage() async {
    if (cameraController == null || !cameraController!.value.isInitialized) {
      Get.snackbar(
        'Error',
        'Camera not initialized',
        snackPosition: SnackPosition.bottom,
      );
      return;
    }

    try {
      final XFile image = await cameraController!.takePicture();
      final File imageFile = File(image.path);
      controller.selfie.value = imageFile;
      // await _validateSelfie(imageFile);
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to capture image: ',
        snackPosition: SnackPosition.bottom,
      );
    }
  }

/*  Future<void> _validateSelfie(File image) async {
    try {
      final inputImage = InputImage.fromFile(image);
      final List<Face> faces = await faceDetector.processImage(inputImage);

      if (faces.isEmpty) {
        Get.snackbar(
          'No Face Detected',
          'Please ensure your face is clearly visible',
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.red[200],
        );
        controller.selfie.value = null;
        return;
      }

      final Face face = faces.first;
      if ((face.headEulerAngleY?.abs() ?? 0) > 30 ||
          (face.headEulerAngleZ?.abs() ?? 0) > 30) {
        Get.snackbar(
          'Face Angle Issue',
          'Please face directly forward',
          snackPosition: SnackPosition.bottom,
          backgroundColor: Colors.orange[200],
        );
      }
    } catch (e) {
      Get.snackbar(
        'Validation Error',
        'Failed to process selfie: ',
        snackPosition: SnackPosition.bottom,
      );
      controller.selfie.value = null;
    } finally {
      await faceDetector.close();
    }
  }*/

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isLight = theme.brightness == Brightness.light;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Capture Selfie'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: Obx(() => Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                const ClayProgress(
                  currentStep: 4,
                  totalSteps: 5,
                ),
                const SizedBox(height: 30),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(30),
                      boxShadow: [
                        BoxShadow(
                          color: isLight ? Colors.white : Colors.black12,
                          offset: const Offset(-6, -6),
                          blurRadius: 12,
                        ),
                        BoxShadow(
                          color:
                              isLight ? Colors.grey.shade400 : Colors.black26,
                          offset: const Offset(6, 6),
                          blurRadius: 12,
                        ),
                      ],
                    ),
                    child: Center(
                      child: controller.selfie.value == null
                          ? _buildCameraPreview()
                          : _buildSelfiePreview(theme),
                    ),
                  ),
                ),
              ],
            ),
          )),
      floatingActionButton:
          controller.selfie.value == null ? _buildCaptureButton(theme) : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildCameraPreview() {
    if (!isCameraInitialized) {
      return const Center(child: CircularProgressIndicator());
    }
    return ClipRRect(
      borderRadius: BorderRadius.circular(20),
      child: CameraPreview(cameraController!),
    );
  }

  Widget _buildSelfiePreview(ThemeData theme) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(20),
          child: Image.file(controller.selfie.value!),
        ),
        Positioned(
          bottom: 20,
          right: 20,
          child: FloatingActionButton(
            backgroundColor: theme.colorScheme.surface,
            onPressed: () => Get.to(() => ReviewSubmitPage()),
            child: Icon(Icons.check, color: theme.colorScheme.primary),
          ),
        ),
      ],
    );
  }

  Widget _buildCaptureButton(ThemeData theme) {
    return Container(
      height: 80,
      width: 80,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: theme.brightness == Brightness.light
                ? Colors.white
                : Colors.black12,
            offset: const Offset(-4, -4),
            blurRadius: 8,
          ),
          BoxShadow(
            color: theme.brightness == Brightness.light
                ? Colors.grey.shade400
                : Colors.black26,
            offset: const Offset(4, 4),
            blurRadius: 8,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(40),
          onTap: _captureImage,
          child: Icon(
            Icons.camera_alt,
            size: 32,
            color: theme.colorScheme.primary,
          ),
        ),
      ),
    );
  }
}
