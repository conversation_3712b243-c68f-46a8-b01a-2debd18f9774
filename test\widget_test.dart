import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

class RestartErrorWidget extends StatelessWidget {
  final VoidCallback onRestartPressed;

  const RestartErrorWidget({
    Key? key,
    required this.onRestartPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.error_outline, color: Colors.red, size: 60),
              const SizedBox(height: 16),
              const Text(
                'App initialization failed',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              const Text(
                'Please restart the app or contact support if the issue persists.',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: onRestartPressed,
                child: const Text('Restart App'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

void main() {
  testWidgets('Error UI Restart App button should call restart callback', 
    (WidgetTester tester) async {
      // Track if button was pressed
      bool restartButtonPressed = false;
      
      // Create a minimal Material app with the error recovery UI
      await tester.pumpWidget(
        MaterialApp(
          home: RestartErrorWidget(
            onRestartPressed: () {
              restartButtonPressed = true;
            },
          ),
        ),
      );

      // Verify the error UI is displayed correctly
      expect(find.text('App initialization failed'), findsOneWidget);
      expect(find.text('Restart App'), findsOneWidget);
      
      // Tap the restart button
      await tester.tap(find.text('Restart App'));
      await tester.pump();
      
      // Verify that the callback was called
      expect(restartButtonPressed, isTrue);
    }
  );
}
