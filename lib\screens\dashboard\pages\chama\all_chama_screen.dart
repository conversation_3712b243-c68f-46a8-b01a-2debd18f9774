import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/chama/create_chama.dart';
import 'package:onekitty/screens/dashboard/pages/chama/widgets/single_chama.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../utils/utils_exports.dart';

class AllChamaScreen extends StatefulWidget {
  const AllChamaScreen({super.key});

  @override
  State<AllChamaScreen> createState() => _AllChamaScreenState();
}

class _AllChamaScreenState extends State<AllChamaScreen> {
  final ChamaController chamaController = Get.put(ChamaController());
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  TextEditingController searchController = TextEditingController();
  List filteredChamas = [];
  String greeting = getGreeting();
  final RefreshController _refreshController =
      RefreshController(initialRefresh: true);

  void _onRefresh() async {
    try {
      await chamaController.getUserChamas();
      chamaController.reset();
      _refreshController.refreshCompleted();
    } catch (e) {
      _refreshController.refreshCompleted();
      ToastUtils.showErrorToast(
        context,
        "Error",
        chamaController.apiMessage.string,
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _onRefresh();
    chamaController.getConfigs();
    filteredChamas = chamaController.chamas;
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBarWithImage(context),
      body: SmartRefresher(
        onRefresh: _onRefresh,
        controller: _refreshController,
        child: Column(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 10.h),
            Obx(
              () => Padding(
                padding: EdgeInsets.only(left: 20.w, right: 20.w),
                child: chamaController.chamas.isEmpty
                    ? const SizedBox()
                    : Row(
                        children: [
                          Text(
                            "My Chama",
                            style: theme.textTheme.titleLarge,
                            // ignore: deprecated_member_use
                            textScaleFactor: 0.8,
                          ),
                          const Spacer(),
                          CustomElevatedButton(
                            onPressed: () {
                              Get.toNamed(NavRoutes.chamaStepper);
                            },
                            width: 150.w,
                            height: 40.h,
                            text: "Create a chama",
                            buttonTextStyle: TextStyle(
                                fontSize: 10.h,
                                fontWeight: FontWeight.bold,
                                color: Colors.white),
                            leftIcon: Container(
                              margin: EdgeInsets.only(right: 2.w),
                              child: CustomImageView(
                                imagePath: AssetUrl.imgPlus,
                                height: 18.h,
                                width: 18.w,
                              ),
                            ),
                          ),
                        ],
                      ),
              ),
            ),
            SizedBox(
              height: 15.h,
            ),
            Obx(
              () => chamaController.chamas.isEmpty
                  ? const SizedBox()
                  : Padding(
                      padding: EdgeInsets.only(left: 20.w, right: 20.w),
                      child: SizedBox(
                        height: 50.h,
                        child: TextFormField(
                          onTap: () async {},
                          autofocus: false,
                          controller: searchController,
                          decoration: const InputDecoration(
                            prefixIcon: Icon(Icons.search),
                            hintText: "Chama name",
                          ),
                          onChanged: (value) {
                            filteredChamas = chamaController.chamas
                                .where((chama) => chama.chama!.title!
                                    .toLowerCase()
                                    .contains(value.toLowerCase()))
                                .toList();
                            setState(() {});
                          },
                        ),
                      ),
                    ),
            ),
            SizedBox(
              height: 7.h,
            ),
            Expanded(
              child: GetX(
                init: ChamaController(),
                initState: (state) {
                  Future.delayed(Duration.zero, () async {
                    chamaController.isChamaLoading(true);
                    try {
                      await state.controller?.getUserChamas();
                      chamaController.reset();
                    } catch (e) {
                      chamaController.isChamaLoading(false);
                      ToastUtils.showErrorToast(context, "Error while loading",
                          chamaController.apiMessage.string);
                      // throw e;
                    }
                    chamaController.isChamaLoading(false);
                  });
                },
                builder: (ChamaController chamaController) {
                  if (chamaController.isChamaLoading.isTrue) {
                    return SizedBox(
                      height: SizeConfig.screenHeight * .33,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SpinKitDualRing(
                              color: ColorUtil.blueColor,
                              lineWidth: 4.sp,
                              size: 40.0.sp,
                            ),
                            const Text(
                              "loading..",
                              style: TextStyle(
                                color: Colors.white,
                              ),
                            )
                          ],
                        ),
                      ),
                    );
                  }
                  if (chamaController.chamas.isEmpty) {
                    return const CrtChama();
                  } else if (filteredChamas.isEmpty) {
                    return Padding(
                      padding: EdgeInsets.only(top: 15.h),
                      child: const Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Center(child: Text("Chama not found")),
                        ],
                      ),
                    );
                  } else if (filteredChamas.isNotEmpty) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20.0, vertical: 20.0),
                      child: Column(
                        children: [
                          Expanded(
                            child: ListView.separated(
                              controller: chamaController.chcontroller,
                              physics: const BouncingScrollPhysics(),
                              separatorBuilder: (context, index) => SizedBox(
                                height: 12.h,
                              ),
                              itemCount: filteredChamas.length,
                              itemBuilder: (context, index) {
                                final chama = filteredChamas[index];
                                return SingleChamaWidget(
                                  chama: chama,
                                  chamaDts: chama.chama ?? Chama(),
                                );
                              },
                            ),
                          ),
                          if (chamaController.loadingMore.isTrue)
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(vertical: 8.0),
                              child: Column(
                                children: [
                                  SpinKitDualRing(
                                    color: ColorUtil.blueColor,
                                    lineWidth: 4.sp,
                                    size: 40.0.sp,
                                  ),
                                  const Text(
                                    "loading..",
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    );
                  }

                  return const CrtChama();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
