import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/events/categories_model.dart';
import 'package:onekitty/screens/dashboard/pages/events/create_event/create_event_page.dart';
import 'package:onekitty/screens/dashboard/pages/events/create_event/tickets.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:dio/dio.dart' as dios;

class CreateEventController extends GetxController implements GetxService {
  final HttpService apiProvider = Get.find();
  final logger = Get.find<Logger>();
  RxString apiMessage = ''.obs;
  RxString checkoutId = ''.obs;
  RxBool isloading = false.obs;
  RxList<CategoriesModel> categories = <CategoriesModel>[].obs;
  RxBool isLoadingCategories = false.obs;
  RxBool isUploading = false.obs;
  RxList<Map<String, dynamic>> eventMedia = <Map<String, dynamic>>[].obs;

  RxInt eventId = 0.obs;
  final eventName = "".obs;
  final eventUsername = "".obs;
  final kittyId = 0.obs;

  Future createEvent({
    required String title,
    required String username,
    required String description,
    required String phoneNumber,
    required String email,
    required String locationTip,
    required String venue,
    required int? referralCode,
    required double lat,
    required double long,
    required int catId,
    required String startDate,
    required List<Map<String, dynamic>> eventMedia,
    required String endDate,
  }) async {
    isloading(true);
    try {
      // Validate inputs before making API call
      if (title.isEmpty ||
          description.isEmpty ||
          phoneNumber.isEmpty ||
          email.isEmpty) {
        throw Exception("Required fields cannot be empty");
      }

      var res = await apiProvider
          .request(url: ApiUrls.CREATEEVENT, method: Method.POST, params: {
        "title": title,
        "username": username,
        "description": description,
        "phone_number": phoneNumber,
        "email": email,
        "location_tip": locationTip,
        "venue": venue,
        "referral_code": referralCode,
        "latitude": lat,
        "longitude": long,
        "category_id": catId,
        "start_date": startDate,
        "end_date": endDate,
        "event_media": eventMedia,
      });

      if (res.data["status"] ?? false) {
        try {
          eventId.value = res.data["data"]["event"]["ID"] ?? 0;
          kittyId.value = res.data["data"]["event"]["kitty_id"] ?? 0;
          eventName.value = res.data["data"]["event"]["title"] ?? "";
          eventUsername.value = res.data["data"]["event"]["username"] ?? "";
          return eventId.value; // Return event ID for success case
        } catch (parseError) {
          logger.e("Error parsing response data: $parseError");
          throw Exception("Failed to parse event data");
        }
      } else {
        Get.snackbar('Error', res.data['message'] ?? 'Could not create event',
            backgroundColor: Colors.red);
        throw Exception(res.data['message'] ?? 'Could not create event');
      }
    } catch (e) {
      logger.e("Create event error: $e");
      apiMessage("Error creating event. Please try again.");
      throw e; // Rethrow to allow proper error handling in UI
    } finally {
      isloading(false);
    }
  }

  Future<bool> createTickets({
    required int eventId,
    required List<TicketUpload> tickets,
  }) async {
    try {
      isloading(true);
      var res = await apiProvider
          .request(url: ApiUrls.ADDTICKETS, method: Method.POST, params: {
        "event_id": eventId,
        "tickets": tickets.map((e) {
          return {
            "title": e.ticketTitle,
            "quantity": e.ticketQuantity,
            "price": e.ticketPrice,
            "ticket_type": e.ticketType.toUpperCase(),
            "group_size": e.ticketGroupSize,
            "description": e.ticketDescription,
            "start_date": e.ticketStartDate,
            "end_date": e.ticketEndDate
          };
        }).toList()
      });
      if (res.data["status"] ?? false) {
        Get.snackbar('success', '${res.data['message']}',
            backgroundColor: Colors.green);
        return true;
      } else {
        Get.snackbar('Error', res.data['message'] ?? 'Error Creating Ticket',
            backgroundColor: Colors.red);
        return false;
      }
    } catch (e) {
      isloading(false);
      logger.e(e);
      apiMessage("Error,Please try again");
      return false;
    } finally {
      isloading(false);
    }
  }

  Future<bool> addSocialMedia({
    required int eventId,
    required List media,
    required String? facebook,
    required String? tiktok,
    required String? instagram,
    required String? youtube,
    required String? twitter,
    required String? hearthis,
    required String? website,
  }) async {
    try {
      isloading(true);

      // Validate URLs if provided
      final urlsToValidate = [
        facebook,
        tiktok,
        instagram,
        youtube,
        twitter,
        hearthis,
        website
      ].where((url) => url != null && url.isNotEmpty);

      for (final url in urlsToValidate) {
        if (!Uri.tryParse(url!)!.hasAbsolutePath) {
          throw Exception('Invalid URL format: $url');
        }
      }

      var res = await apiProvider
          .request(url: ApiUrls.ADDSOCIALMEDIA, method: Method.POST, params: {
        "event_id": eventId,
        "media": media,
        "SocialAccounts": {
          "facebook": facebook,
          "tiktok": tiktok,
          "Instagram": instagram,
          "youtube": youtube,
          "twitter": twitter,
          "hearthis": hearthis,
          "website": website
        }
      });

      if (res.data["status"] == true) {
        return true;
      }
      throw Exception(res.data["message"] ?? "Failed to add social media");
    } catch (e) {
      logger.e("Social media error: $e");
      Get.snackbar('Error', e.toString(), backgroundColor: Colors.red);
      return false;
    } finally {
      isloading(false);
    }
  }

  RxList<Map<String, String>> bannerList = <Map<String, String>>[].obs;
  RxInt category = 0.obs;
  Rx<CategoriesModel?>? selCategory = Rx<CategoriesModel?>(null);

  Future<String?> uploadFile(
      {required String path, required String fileName}) async {
    isUploading(true);

    try {
      var data = dios.FormData.fromMap({
        'file': await dios.MultipartFile.fromFile(path, filename: fileName),
        'bucket': 'onekitty'
      });
      final response = await apiProvider.request(
        url: "${HttpService.baseUrl!}${ApiUrls.UPLOADFILE}",
        method: Method.POST,
        formdata: data, // Pass the FormData
      );
      isUploading(false);
      if (response.data['status'] ?? false) {
        return response.data['data']['file_path'];
      } else {
        return null;
      }
    } catch (e) {
      isUploading(false);
      logger.e('Error uploading file: $e');
      isLoadingCategories(false);
      return null;
    }
  }

  Future<void> getCategories() async {
    try {
      categories.clear();
      categories([]);
      isLoadingCategories(true);
      final httpService = HttpService();
      final dio = httpService.initializeDio();
      final response = await dio.get(ApiUrls.GETCATEGORIES);
      if (response.data != null) {
        final _returneddata = response.data['data']['categories'] as List;
        categories = _returneddata
            .map((item) {
              return CategoriesModel.fromJson(item);
            })
            .toList()
            .obs;
      } else {
        logger.v('No data or unexpected response structure');
      }
      isLoadingCategories(false);
    } catch (e) {
      logger.e('Error fetching events: $e');
      isLoadingCategories(false);
    }
  }

  //tickets controller
  RxList<TicketsModel> tickets = <TicketsModel>[].obs;
  var slotType = 1.obs;
}
