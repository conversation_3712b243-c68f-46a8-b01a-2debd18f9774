import 'dart:io';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/instance_manager.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/controllers/auth_controller.dart';
import 'package:onekitty/controllers/beneficiary_controller.dart';
import 'package:onekitty/controllers/bulksms_controller.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/controllers/events/create_event_controller.dart';
import 'package:onekitty/controllers/events/edit_event_controller.dart';
import 'package:onekitty/controllers/events/edit_ticket_controller.dart';
import 'package:onekitty/controllers/events/events_controller.dart';
import 'package:onekitty/controllers/events/invite_page_controller.dart';
import 'package:onekitty/controllers/events/invite_users_controller.dart';
import 'package:onekitty/controllers/events/payments_page_controller.dart';
import 'package:onekitty/controllers/events/signatory_controller.dart';
import 'package:onekitty/controllers/events/transfer_page_controller.dart';
import 'package:onekitty/controllers/events/verify_ticket_controller.dart';
import 'package:onekitty/controllers/events/view_single_event.dart';
import 'package:onekitty/controllers/events/vieweventcontroller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/loader_easy.dart';
import 'package:onekitty/screens/bottom_navbar_screens/botton_navigation_section/bottom_nav_section.dart';
import 'package:onekitty/screens/dashboard/pages/events/create_event/time_and_location.dart';
import 'package:onekitty/screens/onboarding/updateKYC/controllers/kyc_controller.dart';
import 'package:onekitty/services/analytics.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/custom_logger.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/services/location_service.dart' show LocationService;
import 'package:onekitty/utils/cache_keys.dart';
import 'package:onekitty/services/location_service.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_device_imei/flutter_device_imei.dart';

// Main service initialization function with optimized loading
Future<void> servicesInitialize() async {
  // Critical services first
  await GetStorage.init();
  Loader.configLoader();
  Get.lazyPut(() => GetStorage(), fenix: true);
  await getDeviceInfo();
  
  // Set up logging only once
  Get.lazyPut(() => Logger(filter: CustomLogFilter()), fenix: true);
  
  // Initialize critical controllers
  Get.put(HttpService());
  Get.put(AuthenticationController());
  
  // Initialize network service
  HttpService httpService = Get.find();
  httpService.initializeDio();
  
  // Start non-critical initializations in parallel
  // await Future.wait([
  //   getBaseUrl(),
  // ]);
  
  // Initialize controllers with lazy loading
  _initLazyControllers();
  
  // Initialize location service
  Get.put(LocationService());
  
  // Initialize browser components if needed
  await initInappWebview();
  
  // Initialize analytics in the background
  Future.delayed(const Duration(milliseconds: 500), () {
    AnalyticsEngine.init();
  });
}



// Initialize controllers with lazy loading for better startup performance
void _initLazyControllers() {
  // Core functionality controllers
  Get.lazyPut(() => UserKittyController(), fenix: true);
  Get.lazyPut(() => ContributeController(), fenix: true);
  Get.lazyPut(() => BulkSMSController(), fenix: true);
  Get.lazyPut(() => BottomNavController(), fenix: true);
  Get.lazyPut(() => KittyController(), fenix: true);
  Get.lazyPut(() => ChamaController(), fenix: true);
  Get.lazyPut(() => GlobalControllers(), fenix: true);
  
  // Event-related controllers - these can be loaded later as they aren't needed immediately
  Get.lazyPut(() => KYCController(), fenix: true);
  Get.lazyPut(() => Eventcontroller(), fenix: true);
  Get.lazyPut(() => CreateEventController(), fenix: true);
  Get.lazyPut(() => TimeAndLocationController(), fenix: true);
  Get.lazyPut(() => EditTicketController(), fenix: true);
  Get.lazyPut(() => EditEventController(), fenix: true);
  Get.lazyPut(() => ViewSingleEventController(), fenix: true);
  Get.lazyPut(() => PaymentsController(), fenix: true);
  Get.lazyPut(() => InvitePageController(), fenix: true);
  Get.lazyPut(() => VerifyTicketController(), fenix: true);
  Get.lazyPut(() => TransferPageController(), fenix: true);
  Get.lazyPut(() => SignatoryTransactionController(), fenix: true);
  Get.lazyPut(() => ViewEventController(), fenix: true);
  Get.lazyPut(() => InviteUsersController(), fenix: true);
  Get.lazyPut(()=>ChamaDataController(), fenix: true);
  Get.lazyPut(() => BeneficiaryController(), fenix: true);
}

// Fetch device information
Future<void> getDeviceInfo() async {
  final deviceInfo = DeviceInfoPlugin();
  final box = GetStorage();

  String? imei = await FlutterDeviceImei.instance.getIMEI();

  if (Platform.isAndroid) {
    final androidInfo = await deviceInfo.androidInfo;
    await box.write(CacheKeys.deviceId, androidInfo.id);
    await box.write(CacheKeys.deviceModel, androidInfo.model);
    await box.write(
        CacheKeys.imeiNumber,
        imei ??
            androidInfo
                .serialNumber); // Using device ID as IMEI is no longer accessible
  } else if (Platform.isIOS) {
    final iosInfo = await deviceInfo.iosInfo;
    await box.write(CacheKeys.deviceId, iosInfo.identifierForVendor);
    await box.write(CacheKeys.deviceModel, iosInfo.model);
    await box.write(CacheKeys.imeiNumber, imei ?? iosInfo.identifierForVendor);
  }
}

// Fetch base URL from remote config
Future<void> getBaseUrl() async {
  debugPrint("------Getting url-------");
  bool maintenance = false;
  final remoteConfig = FirebaseRemoteConfig.instance;
  await remoteConfig.setDefaults({
    "base_url_dev": ApiUrls.BASE_URL_DEV,
    "base_url": ApiUrls.BASE_URL_LIVE,
    "maintainance": maintenance
  });
  await remoteConfig.setConfigSettings(RemoteConfigSettings(
    fetchTimeout: const Duration(minutes: 1),
    minimumFetchInterval: const Duration(minutes: 15),
  ));
  // Fetch remote config values
  await remoteConfig.fetch();
  await remoteConfig.activate();
  String baseurlDev = remoteConfig.getString("base_url_dev");
  String baseurlProd = remoteConfig.getString("base_url");
  ApiUrls.BASE_URL_DEV = baseurlDev;
  ApiUrls.BASE_URL_LIVE = baseurlProd;
  maintenance = remoteConfig.getBool("maintainance");
  GetStorage().write(CacheKeys.maintainance, maintenance);
  debugPrint("===========URL FETCHED============");
}

// Initialize WebView if needed
Future<void> initInappWebview() async {
  if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
    await InAppWebViewController.setWebContentsDebuggingEnabled(kDebugMode);
  }
}
