// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart'; 
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/events/view_single_event.dart';
import 'package:onekitty/models/transac_kitt_model.dart';
import 'package:onekitty/models/user_kitties_model.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/models/user_transaction_model.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/excel/excel_func.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/statement_widget.dart';
import 'package:onekitty/services/share_whatsapp_service.dart' show ShareWhatsapp;
import 'package:open_file/open_file.dart';
import 'package:share_plus/share_plus.dart';
import '../../../../../../utils/utils_exports.dart'; 

class ExportContentWidget extends StatelessWidget {
  Item? transaction;
  bool singleTrans;

  ExportContentWidget({
    super.key,
    this.transaction,
    required this.singleTrans,
  });
  UserKittyController userController = Get.put(UserKittyController());

  final KittyController kittyController = Get.put(KittyController());

  @override
  Widget build(BuildContext context) {
    final DateFormat format = DateFormat.MMMEd().add_jms();
    DateTime createdAt = transaction?.createdAt?.toLocal() ?? DateTime.now();
    return Container(
      width: double.maxFinite,
      padding: EdgeInsets.symmetric(
        horizontal: 20.w,
        vertical: 32.h,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 12.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomImageView(
                      imagePath: AssetUrl.imgFile,
                      height: 24.h,
                      width: 24.w,
                    ),
                    Padding(
                      padding: EdgeInsets.only(
                        left: 12.w,
                        top: 3.h,
                      ),
                      child: InkWell(
                        onTap: () {
                          if (singleTrans) {
                            Get.to(() => SingleStatementPage(
                                  isContributions: false,
                                  userTransactions: transaction,
                                ));
                          } else {
                            Get.to(
                              () => UserStatementPage(
                                  isContributions: true,
                                  userTransactions:
                                      userController.alltransactions),
                            );
                          }
                        },
                        child: Text(
                          "Export to PDF",
                          style: CustomTextStyles.titleSmallGray90001,
                        ),
                      ),
                    ),
                  ],
                ),
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: CustomImageView(
                    imagePath: AssetUrl.imgIconoirCancel,
                    height: 24.h,
                    width: 24.w,
                  ),
                ),
              ],
            ),
          ),
          if (!singleTrans) SizedBox(height: 23.h),
          if (!singleTrans)
            Padding(
              padding: EdgeInsets.only(left: 12.w),
              child: Row(
                children: [
                  CustomImageView(
                    imagePath: AssetUrl.imgMicrosoftexcellogo,
                    height: 24.h,
                    width: 24.w,
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      left: 12.w,
                      top: 3.h,
                    ),
                    child: InkWell(
                      onTap: () async {
                        // Show loading indicator while Excel is being generated
                        showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (BuildContext context) {
                            return const Dialog(
                              child: Padding(
                                padding: EdgeInsets.all(20.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    CircularProgressIndicator(),
                                    SizedBox(height: 16),
                                    Text('Generating Excel file...'),
                                  ],
                                ),
                              ),
                            );
                          },
                        );
                        
                        // Generate Excel file in isolate
                        String filePath;
                        try {
                          filePath = await userExcel();
                          // Close loading dialog
                          Navigator.pop(context);
                          
                          // Show options dialog
                          showModalBottomSheet(
                            context: context,
                            builder: (BuildContext context) {
                              return Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    ListTile(
                                      leading: const Icon(Icons.file_open),
                                      title: const Text('Open File'),
                                      onTap: () {
                                        Navigator.pop(context);
                                        OpenFile.open(filePath);
                                      },
                                    ),
                                    ListTile(
                                      leading: const Icon(Icons.share),
                                      title: const Text('Share'),
                                      onTap: () {
                                        Navigator.pop(context);
                                        Share.shareXFiles([XFile(filePath)]);
                                      },
                                    ),
                                  ],
                                ),
                              );
                            }
                          );
                        } catch (e) {
                          // Close loading dialog
                          Navigator.pop(context);
                          
                          // Show error dialog
                          showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return AlertDialog(
                                title: const Text('Error'),
                                content: Text('Failed to generate Excel file: $e'),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(context),
                                    child: const Text('OK'),
                                  ),
                                ],
                              );
                            },
                          );
                        }
                      },
                      child: Text(
                        "Export to Excel",
                        style: CustomTextStyles.titleSmallGray90001,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          SizedBox(height: 23.h),
          Padding(
            padding: EdgeInsets.only(left: 12.w),
            child: Row(
              children: [
                CustomImageView(
                  imagePath: AssetUrl.imgSettings,
                  height: 24.h,
                  width: 24.w,
                ),
                Padding(
                  padding: EdgeInsets.only(
                    left: 12.w,
                    top: 3.h,
                  ),
                  child: InkWell(
                    onTap: () async {
                      String shareMsg =
                          // "Phone Number: ${details?.phoneNumber}\nAmount: KSH ${details?.amount}\nTransaction Code: ${details?.resultCode}\nStatus: ${details?.resultDesc}\nChannel:${details?.channel}\nDate: ${format.format(createdAt.toLocal())}\nKitty: https://onekitty.co.ke/kitty/${details?.kittyId}";

                          "Phone Number: ${userController.alltransactions.first.phoneNumber ?? "Anonymous"}\nAmount: KSH ${userController.alltransactions.first.amount}\nTransaction Code: ${userController.alltransactions.first.transactionCode}\nStatus: ${userController.alltransactions.first.status}\nDate: ${format.format(createdAt.toLocal())}";
                      await Share.share(shareMsg,
                          subject: 'Transaction details');
                    },
                    child: Text(
                      "Export to Text",
                      style: CustomTextStyles.titleSmallGray90001,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 23.h),
        ],
      ),
    );
  }
}

class ExportContentWidget2 extends StatefulWidget {
  final TransactionModel? details;
  final Kitty? kitty;
  bool singleTrans;
  final int? eventId;

  ExportContentWidget2({
    super.key,
    this.details,
    this.kitty,
    required this.singleTrans,
    this.eventId,
  });

  @override
  State<ExportContentWidget2> createState() => _ExportContentWidget2State();
}

class _ExportContentWidget2State extends State<ExportContentWidget2> {
  KittyController kittyController = Get.put(KittyController());
  final DataController dataController = Get.put(DataController());

  String shareMsg = "";

  Future<void> fetchMessage() async {
    final res = await kittyController.shareKittyTrans(
        id: (dataController.kitty.value.kitty?.id ?? 0));
    if (res) {
      if (mounted) {
        setState(() {
          shareMsg = kittyController.textmessage.toString();
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    fetchMessage();
  }
  // final DateFormat format = DateFormat.MMMEd().add_jms();

  @override
  Widget build(BuildContext context) {
    final event = widget.eventId != null
        ? Get.find<ViewSingleEventController>().event.value
        : null;
    final DateFormat format = DateFormat.MMMEd().add_jms();
    DateTime createdAt = widget.details?.createdAt ?? DateTime.now();
    return Container(
      width: double.maxFinite,
      padding: EdgeInsets.symmetric(
        horizontal: 20.w,
        vertical: 32.h,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 12.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomImageView(
                      imagePath: AssetUrl.imgFile,
                      height: 24.h,
                      width: 24.w,
                    ),
                    InkWell(
                      onTap: () {
                        if (widget.singleTrans) {
                          Get.to(
                            () => SingleStatementPage(
                              eventId: widget.eventId,
                              isContributions: true,
                              kitty: widget.kitty,
                              kittyTransaction: widget.details,
                            ),
                          );
                        } else {
                          Get.to(
                            () => StatementPage(
                              eventId: widget.eventId,
                              isContributions: true,
                              kitty: widget.kitty,
                              transactions: kittyController.transactionsKitty,
                            ),
                          );
                        }
                      },
                      child: Padding(
                        padding: EdgeInsets.only(
                          left: 12.w,
                          top: 3.h,
                        ),
                        child: Text(
                          "Export to PDF",
                          style: CustomTextStyles.titleSmallGray90001,
                        ),
                      ),
                    ),
                  ],
                ),
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: CustomImageView(
                    imagePath: AssetUrl.imgIconoirCancel,
                    height: 24.h,
                    width: 24.w,
                  ),
                ),
              ],
            ),
          ),
          if (!widget.singleTrans) SizedBox(height: 23.h),
          if (!widget.singleTrans)
            Padding(
              padding: EdgeInsets.only(left: 12.w),
              child: Row(
                children: [
                  CustomImageView(
                    imagePath: AssetUrl.imgMicrosoftexcellogo,
                    height: 24.h,
                    width: 24.w,
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      left: 12.w,
                      top: 3.h,
                    ),
                    child: InkWell(
                      onTap: () async {
                        // Show loading indicator while Excel is being generated
                        showDialog(
                          context: context,
                          barrierDismissible: false,
                          builder: (BuildContext context) {
                            return const Dialog(
                              child: Padding(
                                padding: EdgeInsets.all(20.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    CircularProgressIndicator(),
                                    SizedBox(height: 16),
                                    Text('Generating Excel file...'),
                                  ],
                                ),
                              ),
                            );
                          },
                        );
                        
                        // Generate Excel file in isolate
                        String filePath;
                        try {
                          filePath = await createExcel(
                              isKitty: true, eventId: widget.eventId);
                          // Close loading dialog
                          Navigator.pop(context);
                          
                          // Show options dialog
                          showModalBottomSheet(
                            context: context,
                            builder: (BuildContext context) {
                              return Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    ListTile(
                                      leading: const Icon(Icons.file_open),
                                      title: const Text('Open File'),
                                      onTap: () {
                                        Navigator.pop(context);
                                        OpenFile.open(filePath);
                                      },
                                    ),
                                    ListTile(
                                      leading: const Icon(Icons.share),
                                      title: const Text('Share'),
                                      onTap: () {
                                        Navigator.pop(context);
                                        Share.shareXFiles([XFile(filePath)]);
                                      },
                                    ),
                                  ],
                                ),
                              );
                            }
                          );
                        } catch (e) {
                          // Close loading dialog
                          Navigator.pop(context);
                          
                          // Show error dialog
                          showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return AlertDialog(
                                title: const Text('Error'),
                                content: Text('Failed to generate Excel file: $e'),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(context),
                                    child: const Text('OK'),
                                  ),
                                ],
                              );
                            },
                          );
                        }
                      },
                      child: Padding(
                        padding: EdgeInsets.only(
                          left: 12.w,
                          top: 3.h,
                        ),
                        child: Text(
                          "Export to Excel",
                          style: CustomTextStyles.titleSmallGray90001,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          SizedBox(height: 23.h),
          Padding(
            padding: EdgeInsets.only(left: 12.w),
            child: Row(
              children: [
                CustomImageView(
                  imagePath: AssetUrl.imgSettings,
                  height: 24.h,
                  width: 24.w,
                ),
                Padding(
                  padding: EdgeInsets.only(
                    left: 12.w,
                    top: 3.h,
                  ),
                  child: InkWell(
                    onTap: () async {
                      if (widget.singleTrans) {
                        String shareMsg =
                            "Title: ${event?.title ?? dataController.kitty.value.kitty?.title ?? ''}\nPhone Number: ${widget.details?.phoneNumber}\nAmount: KSH ${widget.details?.amount}\nName: ${widget.details?.firstName ?? ""} ${widget.details?.secondName ?? ""}\nTransaction Code: ${widget.details?.transactionCode ?? ''}\nDate: ${format.format(createdAt.toLocal())}\n${event != null ? 'Event: https://onekitty.co.ke/event/${event.username}' : 'Kitty: https://onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.id}'}";
                        await Share.share(shareMsg,
                            subject: 'Transaction details');
                      } else {
                        try {
                          await Share.share("${kittyController.textmessage}");
                        } catch (e) {
                          print('Error sharing: $e');
                        }
                      }
                    },
                    child: Text(
                      "Export to Text",
                      style: CustomTextStyles.titleSmallGray90001,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 23.h),
          Padding(
            padding: EdgeInsets.only(left: 12.w),
            child: Row(
              children: [
                CustomImageView(
                  imagePath: AssetUrl.imgVolume,
                  height: 24.h,
                  width: 24.w,
                ),
                InkWell(
                  onTap: () async {
                    Get.back();
                    if (widget.singleTrans) {
                      String shareMsg =
                          "${event != null ? 'Event' : 'Kitty Title'}: ${event?.title ?? dataController.kitty.value.kitty?.title ?? ''}\nPhone Number: ${widget.details?.phoneNumber}\nAmount: KSH ${widget.details?.amount}\nName: ${widget.details?.firstName ?? ""} ${widget.details?.secondName ?? ""}\nTransaction Code: ${widget.details?.transactionCode ?? ''}\nDate: ${format.format(createdAt.toLocal())}\n${event != null ? 'Event: https://onekitty.co.ke/events/${event.username}' : 'Kitty: https://onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.id}'} ";
                      ShareWhatsapp.share(
                        shareMsg,
                      );
                    } else {
                      try {
                       ShareWhatsapp.share("${kittyController.textmessage}");
                      } catch (e) {
                        print('Error sharing: $e');
                      }
                    }
                  },
                  child: Padding(
                    padding: EdgeInsets.only(
                      left: 12.w,
                      top: 3.h,
                    ),
                    child: Text(
                      "WhatsApp message",
                      style: CustomTextStyles.titleSmallGray90001,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 5.h),
        ],
      ),
    );
  }
}
