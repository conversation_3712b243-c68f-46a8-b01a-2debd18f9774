import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:intl/intl.dart';
import 'package:number_paginator/number_paginator.dart';
import 'package:onekitty/models/transac_kitt_model.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/export_widget.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/transaction_item.dart';
import '../../../../../utils/utils_exports.dart';
import 'package:flutter/material.dart';

// ignore_for_file: must_be_immutable
class AllTransactionsScreen extends StatefulWidget {
  final int? kittyId;
  final int? eventId;
  const AllTransactionsScreen({super.key, this.kittyId, this.eventId});

  @override
  State<AllTransactionsScreen> createState() => _AllTransactionsScreenState();
}

class _AllTransactionsScreenState extends State<AllTransactionsScreen> {
  final KittyController controller = Get.find<KittyController>();
  TextEditingController searchController = TextEditingController();
  final DataController dataController = Get.find<
      DataController>(); // ContributeController kittyGoten = Get.find();
  List<String> dropdownItemList = ["code", "Date", "Account No"];
  String selectedFilter = "";

  TextEditingController startDate = TextEditingController();
  TextEditingController endDate = TextEditingController();
  bool singleTrans = false;
  TextEditingController phoneController = TextEditingController();
  TextEditingController codeController = TextEditingController();
  List<TransactionModel> filterbyname = [];
  GlobalKey<NavigatorState> navigatorKey = GlobalKey();

  final dateformat = DateFormat('EE, dd MMMM');

  @override
  void initState() {
    super.initState();
    filterbyname = controller.transactionsKitty;
    // searchController.addListener(_onSearchTextChanged);
  }

  // void _onSearchTextChanged() {
  //   setState(() {});
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          elevation: 0,
          leadingWidth: 120.w,
          leading: GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Row(
              children: [
                const Icon(Icons.navigate_before),
                SizedBox(width: 4.w),
                const Text('Back')
              ],
            ),
          ),
          bottom: PreferredSize(
            preferredSize: Size.fromHeight(90.h),
            child: ColoredBox(
              color: Theme.of(context).scaffoldBackgroundColor,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    SizedBox(width: 12.w),
                       Expanded(
                        child: TextField(
                          controller: searchController,
                          
                          onChanged: (value) {
                            // Update filterByName with the latest data from chamaController
                            if (value.isEmpty) {
                              filterbyname = List.from(controller.transactionsKitty);
                            } else {
                              filterbyname = controller.transactionsKitty
                                  .where((p0) => (p0.firstName?.toLowerCase() ?? '')
                                      .contains(value.toLowerCase()))
                                  .toList();
                            }
                            setState(() {});
                          },
                          decoration: InputDecoration(
                            hintText: "Search transactions...",
                            hintStyle: TextStyle(
                              color: Theme.of(context).hintColor,
                              fontSize: 14.sp,
                            ),
                            filled: true,
                            fillColor: Theme.of(context).scaffoldBackgroundColor,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(24),
                              borderSide: BorderSide.none,
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(24),
                              borderSide: const BorderSide(
                                // color: Theme.of(context).colorScheme.primary,
                                width: 1,
                              ),
                            ),
                            prefixIcon: Icon(
                              Icons.search_rounded,
                              color: Theme.of(context).hintColor,
                              size: 24.h,
                            ),
                            suffixIcon: searchController.text.isNotEmpty
                                ? IconButton(
                                    icon: Icon(Icons.clear_rounded,
                                        color: Theme.of(context).hintColor),
                                    onPressed: () => searchController.clear(),
                                  )
                                : null,
                            contentPadding: EdgeInsets.symmetric(vertical: 12.h),
                          ),
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                      ),
              
                
                    SizedBox(width: 12.w),
                    CustomDropDown(
                      width: 150.w,
                      hintText: "Filter",
                      hintStyle: TextStyle(
                        fontSize: 12.h,
                      ),
                      items: dropdownItemList,
                      prefix: Icon(
                        Icons.filter_alt_rounded,
                        size: 20.h,
                      ),
                      onChanged: (value) {
                        setState(() => selectedFilter = value);
                      },
                    ),
                    SizedBox(width: 12.w),
                  ],
                ),
              ),
            ),
          ),
          title: const Text('Transactions'),
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: Container(
          width: double.maxFinite,
          padding: EdgeInsets.symmetric(vertical: 16.h),
          child: Column(
            children: [
              // ignore: unrelated_type_equality_checks
              if (selectedFilter == "Account No")
                _buildPhoneNoFilterUI(context),
              if (selectedFilter == "code") _buildCodeFilterUI(context),
              if (selectedFilter == "Date") _buildDateFilterUI(context),
              if (selectedFilter == "") SizedBox(height: 3.h),
              Expanded(
                child: Container(
                  height: 869.h,
                  margin: EdgeInsets.symmetric(horizontal: 32.w),
                  child: Align(
                    alignment: Alignment.center,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        startDate.text.isNotEmpty && endDate.text.isNotEmpty ||
                                phoneController.text.isNotEmpty ||
                                codeController.text.isNotEmpty
                            ? _buildFilteredTransactionsList(context)
                            : _buildTransactionsList(context)
                      ],
                    ),
                  ),
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.inversePrimary,
                ),
                child: Obx(() {
                  if (controller.transactionsKitty.isNotEmpty &&
                      controller.results.value!.totalPages! > 0) {
                    return  NumberPaginator(
                        numberPages: controller.results.value!.totalPages ?? 0,
                        onPageChange: (int index) async {
                          await controller.getKittyContributions(
                            kittyId: widget.kittyId ??
                                dataController.kitty.value.kitty?.id ??
                                0,
                            page: index,
                          );
                          setState(() {});
                        },
                      );
                    
                  } else {
                    return Container(); // Empty container when transactionsKitty is empty
                  }
                }),
              )
            ],
          ),
        ),
        floatingActionButton: Padding(
          padding: const EdgeInsets.only(bottom: 60.0),
          child: FilledButton.icon(
            style: FilledButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
            icon: CustomImageView(
              imagePath: AssetUrl.expIcon,
              height: 16.h,
              width: 16.w,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            label: Text(
              'Export',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onPrimary,
              ),
            ),
            onPressed: () {
              showModalBottomSheet(
                context: context,
                builder: (BuildContext context) {
                  return ExportContentWidget2(
                    eventId: widget.eventId,
                    singleTrans: singleTrans,
                  );
                },
              );
            },
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      
    );
  }

  // All the pagenated transactions page

  Widget _buildTransactionsList(BuildContext context) {
    return GetX(
      builder: (KittyController controller) {
        if (controller.loadingTransactions.isTrue) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SpinKitDualRing(
                  color: Theme.of(context).colorScheme.primary,
                  lineWidth: 3.sp,
                  size: 36.sp,
                ),
                SizedBox(height: 8.h),
                Text(
                  "Loading...",
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontSize: 14.sp,
                    fontStyle: FontStyle.italic,
                  ),
                )
              ],
            ),
          );
        } else if (controller.transactionsKitty.isEmpty) {
          return Center(
            child: Text(
              "You have no transactions yet",
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface,
                fontSize: 16.sp,
              ),
            ),
          );
        } else if (filterbyname.isEmpty) {
          return Padding(
            padding: EdgeInsets.only(top: 15.h),
            child: Text(
              "No transactions found",
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface,
                fontSize: 16.sp,
              ),
            ),
          );
        } else if (filterbyname.isNotEmpty) {
          return Expanded(
            child: GroupedListView<TransactionModel, DateTime>(
              elements: filterbyname,
              sort: false,
              // useStickyGroupSeparators: true,
              groupBy: (TransactionModel element) {
                DateTime date = element.createdAt!.toLocal();
                return DateTime(date.year, date.month, date.day);
              },
              groupHeaderBuilder: (value) {
                final date = dateformat.format(value.createdAt!.toLocal());
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    date,
                    style: const TextStyle( 
                      fontWeight: FontWeight.bold,
                      fontSize: 15.0,
                    ),
                  ),
                );
              },
              itemBuilder: (_, TransactionModel item) {
                return TransactionItem(
                  item: item,
                  eventId: widget.eventId,
                );
              },
              separator: const Padding(
                padding: EdgeInsets.symmetric(vertical: 8.0),
              ),
            ),
          );
        } else {
          return Container();
        }
      },
    );
  }

// All the Filtered transactions

  Widget _buildFilteredTransactionsList(BuildContext context) {
    return GetX(
      init: KittyController(),
      builder: (KittyController controller) {
        if (controller.loadingfiltrTransactions.isTrue) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SpinKitDualRing(
                  color: ColorUtil.blueColor,
                  lineWidth: 4.sp,
                  size: 40.0.sp,
                ),
                const Text(
                  "Loading kitty transactions",
                  style: TextStyle(
                    color: Colors.white,
                    fontStyle: FontStyle.italic,
                  ),
                )
              ],
            ),
          );
        } else if (controller.filtrtransactions.isEmpty ||
            // ignore: unnecessary_null_comparison
            controller.filtrtransactions == null) {
          return const Center(
            child: Text("could not find the transactions"),
          );
        } else if (controller.filtrtransactions.isNotEmpty) {
          return Expanded(
            child: GroupedListView<TransactionModel, DateTime>(
              elements: controller.filtrtransactions,
              groupStickyHeaderBuilder: (element) => Text( dateformat.format(element.createdAt!.toLocal())),
              groupBy: (TransactionModel element) {
                DateTime date = element.createdAt!;
                return DateTime(date.year, date.month, date.day);
              },
              stickyHeaderBackgroundColor: Colors.transparent,
              // floatingHeader: false,
              groupHeaderBuilder: (value) {
                final date = dateformat.format(value.createdAt!.toLocal());
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    date,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 20.0,
                    ),
                  ),
                );
              },
              itemBuilder: (_, TransactionModel item) {
                return TransactionItem(
                  item: item,
                  eventId: widget.eventId,
                );
              },
              separator: const Padding(
                padding: EdgeInsets.symmetric(vertical: 8.0),
              ),
            ),
          );
        } else {
          return Expanded(
            child: GroupedListView<TransactionModel, DateTime>(
              elements: controller.transactionsKitty,
              groupBy: (TransactionModel element) {
                DateTime date = element.createdAt!.toLocal();
                return DateTime(date.year, date.month, date.day);
              },
              groupHeaderBuilder: (value) {
                final date = dateformat.format(value.createdAt!.toLocal());
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    date,
                    style: const TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                      fontSize: 12.0,
                    ),
                  ),
                );
              },
              itemBuilder: (_, TransactionModel item) {
                return TransactionItem(item: item, eventId: widget.eventId);
              },
              separator: const Padding(
                padding: EdgeInsets.symmetric(vertical: 8.0),
              ),
            ),
          );
        }
      },
    );
  }


  // Phone filter UI

  Widget _buildPhoneNoFilterUI(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomSearchView(
            width: 170.w,
            controller: phoneController,
            hintText: "Filter phone number",
            contentPadding: EdgeInsets.all(5.h),
            onChanged: (p0) {
              if (p0.length == 10) {
                String newPhoneNumber = p0.substring(1);

                phoneController.text = newPhoneNumber;
                _updateFilter();
              }
            },
          ),
          InkWell(
            onTap: () {
              setState(() {
                selectedFilter = "Filter";
                phoneController.clear();
              });
            },
            child: CustomImageView(
              imagePath: AssetUrl.imgIconoirCancel,
              height: 24.h,
              width: 24.w,
            ),
          ),
        ],
      ),
    );
  }

  ////Transaction filter UI

  Widget _buildCodeFilterUI(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomSearchView(
            width: 170.w,
            controller: codeController,
            hintText: "Filter with code",
            contentPadding: EdgeInsets.all(5.h),
            onChanged: (p0) {
              _updateFilter();
            },
          ),
          InkWell(
            onTap: () {
              setState(() {
                selectedFilter = "Filter";
                codeController.clear();
              });
            },
            child: CustomImageView(
              imagePath: AssetUrl.imgIconoirCancel,
              height: 24.h,
              width: 24.w,
            ),
          ),
        ],
      ),
    );
  }

  ///Date Filtering

  Widget _buildDateFilterUI(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 12.0, right: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 120.w,
            height: 35.h,
            child: TextFormField(
              controller: startDate,
              style: const TextStyle(fontSize: 15),
              readOnly: false,
              onTap: () async {
                final DateTime? pickedDate = await showDatePicker(
                  context: context,
                  initialDate:
                      DateTime.now(), // Set initial date to current date
                  firstDate: DateTime(2000),
                  lastDate:
                      DateTime(2100), // Set the latest date that can be picked
                );

                if (pickedDate != null) {
                  final formattedDate =
                      DateFormat('yyyy-MM-dd').format(pickedDate);
                  startDate.text = formattedDate;
                  // _updateDateFilter();
                }
              },
              decoration: InputDecoration(
                labelText: 'Start Date',
                border: const OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.black87),
                ),
                focusedBorder: const OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.black87)),
                suffixIcon: Icon(
                  Icons.calendar_today,
                  size: 18.h,
                ),
              ),
            ),
          ),
          SizedBox(
            width: 8.w,
          ),
          SizedBox(
            width: 120.w,
            height: 35.h,
            child: TextFormField(
              onEditingComplete: () {
                // _loadFiltrTransactions(startDate.text, endDate.text);
              },
              controller: endDate,
              style: const TextStyle(fontSize: 15),
              readOnly: false,
              onTap: () async {
                final DateTime? pickedDate = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                );

                if (pickedDate != null) {
                  final formattedDate =
                      DateFormat('yyyy-MM-dd').format(pickedDate);
                  endDate.text = formattedDate;
                  _updateFilter();
                }
              },
              decoration: InputDecoration(
                labelText: 'End Date',
                border: const OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.black87),
                ),
                focusedBorder: const OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.black87)),
                suffixIcon: Icon(
                  Icons.calendar_today,
                  size: 18.h,
                ),
              ),
            ),
          ),
          SizedBox(
            width: 8.w,
          ),
          InkWell(
            onTap: () {
              setState(() {
                selectedFilter = "Filter";
                startDate.clear();
                endDate.clear();
              });
            },
            child: CustomImageView(
              imagePath: AssetUrl.imgIconoirCancel,
              height: 24.h,
              width: 24.w,
            ),
          ),
        ],
      ),
    );
  }

  /// Section Widget
  // ignore: unused_element
  Widget _buildRow(BuildContext context) {
    return Container(
      height: 60.h,
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Row(
        // mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: CupertinoSearchTextField(
                onChanged: (value) {
                  filterbyname = controller.transactionsKitty
                      .where((p0) => (p0.firstName?.toLowerCase() ?? '')
                          .contains(value.toLowerCase()))
                      .toList();
                  setState(() {});
                },
                controller: searchController,
                placeholder: 'Search by name',
              ),
            ),
          ),
          const SizedBox(
            width: 5,
          ),
          CustomDropDown(
              fillColor: Colors.blue,
              width: 120.w,
              hintText: "Filter",
              hintStyle: TextStyle(fontSize: 12.h, color: appTheme.indigo500),
              items: dropdownItemList,
              prefix: Container(
                margin: EdgeInsets.fromLTRB(4.w, 11.h, 0.w, 11.h),
                child: CustomImageView(
                    imagePath: AssetUrl.imgIconIndigo500,
                    height: 40.h,
                    width: 18.w),
              ),
              prefixConstraints:
                  BoxConstraints(maxHeight: 40.h, maxWidth: 20.h),
              onChanged: (value) {
                setState(() {
                  selectedFilter = value;
                });
              }),
        ],
      ),
    );
  }

  void _updateFilter() {
    setState(() {
      _fetchFilteredTransactions();
    });
  }

  void _fetchFilteredTransactions() async {
    try {
      await Get.find<KittyController>().getKittyFiltrContributions(
        eventId: widget.eventId,
        kittyId: dataController.kitty.value.kitty?.id ?? 0,
        startDate: startDate.text,
        endDate: endDate.text,
        phoneNumber: phoneController.text,
        code: codeController.text,
      );

      setState(() {
        Get.find<KittyController>().loadingfiltrTransactions(false);
      });
    } catch (e) {
      throw e;
    }
  }
}
