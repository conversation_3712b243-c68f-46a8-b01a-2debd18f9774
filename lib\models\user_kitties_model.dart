// To parse this JSON data, do
//
//     final userKitties = userKittiesFromJson(jsonString);

import 'dart:convert';

import 'package:onekitty/controllers/kitty_controller.dart';

UserKitties userKittiesFromJson(String str) =>
    UserKitties.fromJson(json.decode(str));

String userKittiesToJson(UserKitties data) => json.encode(data.toJson());

class UserKitties {
  bool? status;
  String? message;
  Data? data;

  UserKitties({
    this.status,
    this.message,
    this.data,
  });

  factory UserKitties.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return UserKitties(); // Return a default object if json is null
    }

    return UserKitties(
      status: json["status"],
      message: json["message"],
      data: json["data"] != null ? Data.fromJson(json["data"]) : null,
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };
}

class Data {
  List<UserKitty> userKitties;

  Data({
    required this.userKitties,
  });

  factory Data.fromJson(Map<String, dynamic>? json) {
    if (json == null) return Data(userKitties: []);

    return Data(
      userKitties: (json["user_kitties"] as List<dynamic>? ?? [])
          .map((e) => UserKitty.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        "user_kitties": List<dynamic>.from(userKitties.map((x) => x.toJson())),
      };
}

class UserKitty {
  Kitty? kitty;
  String? kittyStatus;
  String? kittBeneficiaryChannel;
  String? kittyType;
  bool? hasMembership;
  String? paymentRefLabel;
  bool? hasSignatories;
  final double? percentage;
  UserKitty(
      {this.kitty,
      this.kittyStatus,
      this.kittBeneficiaryChannel,
      this.kittyType,
      this.percentage,
      this.hasSignatories,
      this.hasMembership, this.paymentRefLabel});

  factory UserKitty.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return UserKitty(
        kitty: Kitty(),
        kittyStatus: '',
        kittBeneficiaryChannel: '',
        kittyType: '',
        percentage: 0.0,
        hasMembership: false,
        hasSignatories: false,
        paymentRefLabel: 'Payment Ref'
      );
    }

    return UserKitty(
      hasSignatories: json['has_signatory_transaction'] ?? false,
      kitty: json["kitty"] != null ? Kitty.fromJson(json["kitty"]) : Kitty(),
      kittyStatus: json["kitty_status"] ?? '',
      kittBeneficiaryChannel: json["kitt_beneficiary_channel"] ?? '',
      kittyType: json["kitty_type"] ?? '',
      percentage: json['percentage'],
      hasMembership: json['has_membership'] ?? false,
      paymentRefLabel: json['payment_ref_label'] ?? 'Payment Ref'
    );
  }

  Map<String, dynamic> toJson() => {
        "kitty": kitty?.toJson(),
        "has_signatory_transaction": hasSignatories,
        "kitty_status": kittyStatus,
        "kitt_beneficiary_channel": kittBeneficiaryChannel,
        "kitty_type": kittyType,
        "percentage" : percentage,
        "has_membership" : hasMembership,
        "payment_ref_label" : paymentRefLabel
      };
}

class Kitty {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  String? title;
  String? description;
  String? beneficiaryAccount;
  String? beneficiaryChannel;
  String? beneficiaryPhoneNumber;
  DateTime? endDate;
  double? percentage;
  num? balance;
  num? limit;
  int? settlementType;
  String? bennefAccRef;
  int? refererMerchantCode;
  int? kittyType;
  String? phoneNumber;
  List<KittyMediaModel>? media;

  Kitty({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.description,
    this.beneficiaryAccount,
    this.beneficiaryChannel,
    this.beneficiaryPhoneNumber,
    this.endDate,
    this.limit,
    this.refererMerchantCode,
    this.settlementType,
    this.balance,
    this.bennefAccRef,
    this.kittyType,
    this.phoneNumber,
    this.media,
    this.percentage,
  });

  factory Kitty.fromJson(Map<String, dynamic>? json) {
    if (json == null) return Kitty();

    return Kitty(
      id: json["ID"],
      createdAt:
          json["CreatedAt"] == null ? null : DateTime.parse(json["CreatedAt"]),
      updatedAt:
          json["UpdatedAt"] == null ? null : DateTime.parse(json["UpdatedAt"]),
      deletedAt: json["DeletedAt"],
      title: json["title"],
      description: json["description"],
      beneficiaryAccount: json["beneficiary_account"],
      beneficiaryChannel: json["beneficiary_channel"],
      beneficiaryPhoneNumber: json["beneficiary_phone_number"],
      endDate:
          json["end_date"] == null ? null : DateTime.parse(json["end_date"]),
      limit: double.tryParse(json["limit"] ?.toString() ?? ''),
      refererMerchantCode: json["referer_merchant_code"],
      settlementType: json["settlement_type"],
      balance: json["balance"],
      bennefAccRef: json["beneficiary_account_ref"],
      kittyType: json["kitty_type"],
      percentage: double.tryParse( json["percentage"]?.toString() ?? '' ),
      phoneNumber: json["phone_number"] ?? "",
      media: (json["media"] as List<dynamic>?)
              ?.map((item) => KittyMediaModel.fromJson(item))
              .toList() ??
          <KittyMediaModel>[],
    );
  }

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "title": title,
        "description": description,
        "beneficiary_account": beneficiaryAccount,
        "beneficiary_channel": beneficiaryChannel,
        "beneficiary_phone_number": beneficiaryPhoneNumber,
        "end_date": endDate?.toIso8601String(),
        "limit": limit,
        "referer_merchant_code": refererMerchantCode,
        "balance": balance,
        "phone_number": phoneNumber,
        "beneficiary_account_ref": bennefAccRef,
        "kitty_type": kittyType,
        "settlement_type": settlementType,
        "media": media,
        'percentage': percentage,
      };
}
