// ignore_for_file: use_build_context_synchronously

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/models/events/tickets_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';

class EditTicketController extends GetxController implements GetxService {
  var slotType = 1.obs;
  final HttpService apiProvider = Get.find();
  final logger = Get.find<Logger>();
  RxString apiMessage = ''.obs;
  RxBool createTicketstatus = false.obs;
  RxString checkoutId = ''.obs;
  RxBool isloading = false.obs;
  RxBool isFetchingTickets = false.obs;
  Rx<List<Ticket>?>? tickets = Rx<List<Ticket>?>(null);
  final isDeleting = false.obs;
  final isCreatingTicket = false.obs;

  Future<void> fetchTickets(int id) async {
    try {
      isFetchingTickets(true);
      update();
      final response = await apiProvider.request(
          method: Method.GET, url: "${ApiUrls.GETEVENTBYID}$id");
      if (response.data != null) {
        final _returneddata =
            response.data['data']['event'] as Map<String, dynamic>;

        final returnedTickets = Event.fromJson(_returneddata).tickets;
        tickets = Rx<List<Ticket>?>(returnedTickets);
      } else {
        logger.v('No data or unexpected response structure');
      }
      isFetchingTickets(false);
      update();
    } catch (e) {
      logger.e('Error fetching events: $e');
      Future.delayed(Duration.zero, () {
        isFetchingTickets(false);
      });
      update();
    }
  }

  Future createTickets({
    required int eventId,
    required List<Map<String, dynamic>> tickets,
    required BuildContext context,
  }) async {
    isCreatingTicket(true);
    final _data = {
      "event_id": eventId,
      "tickets": tickets,
    };
    print(json.encode(_data));
    try {
      var res = await apiProvider.request(
          url: ApiUrls.ADDTICKETS, method: Method.POST, params: _data);
      if (res.data["status"]) {
        apiMessage(res.data["message"]);
        createTicketstatus(res.data["status"]);
        Get.snackbar('Success', '${res.data['message']}',
            backgroundColor: Colors.green);
        await fetchTickets(eventId);
      } else {
        createTicketstatus(res.data["status"]);

        ToastUtils.showErrorToast(
          autoDismiss: true,
          context,
          'Error',
          res.data['message'],
        );
      }
    } catch (e) {
      logger.e(e);
      apiMessage("Error,Please try again");
      return false;
    } finally {
      isCreatingTicket(false);
    }
  }

  Future deleteTicket({
    required int eventId,
    required Ticket ticket,
    required String description,
    required String type,
    required int quantity,
    required int price,
    required String title,
    required DateTime startDate,
    required DateTime endDate,
    required BuildContext context,
  }) async {
    try {
      isDeleting(true);
      final _data = {
        "ID": ticket.id,
        "CreatedAt": ticket.createdAt?.toUtc().toIso8601String(),
        "UpdatedAt": DateTime.now().toUtc().toIso8601String(),
        "DeletedAt": null,
        "title": title,
        "quantity": quantity,
        "price": price,
        "status": "DELETED",
        "currency": "KES",
        "ticket_type": type.toUpperCase(),
        "description": description,
        "event_id": eventId,
        "start_date": startDate.toUtc().toIso8601String(),
        "end_date": endDate.toUtc().toIso8601String(),
      };
      var res = await apiProvider.request(
          url: ApiUrls.EDITTICKET, method: Method.PUT, params: _data);
      if (res.data["status"]) {
        Get.snackbar('Success', "${res.data["message"]}");
        await fetchTickets(eventId);
        return true;
      } else {
        ToastUtils.showErrorToast(
          context,
          autoDismiss: true,
          'Error',
          res.data['message'],
        );
        return false;
      }
    } catch (e) {
      logger.e(e);
      ToastUtils.showErrorToast(
        context,
        'Error',
        "an error occured",
      );
      apiMessage("Error,Please try again");
      return false;
    } finally {
      isDeleting(false);
    }
  }

  Future<bool> editTicket({
    required int eventId,
    required Ticket ticket,
    required String description,
    required String type,
    required int quantity,
    required int price,
    required String title,
    required DateTime startDate,
    required DateTime endDate,
    required BuildContext context,
    int? groupSize,
  }) async {
    try {
      isloading(true);
      final _data = {
        "ID": ticket.id,
        "CreatedAt": ticket.createdAt?.toUtc().toIso8601String(),
        "UpdatedAt": DateTime.now().toUtc().toIso8601String(),
        "DeletedAt": null,
        "title": title,
        "quantity": quantity,
        "price": price,
        "status": ticket.status?.toString(),
        "currency": "KES",
        "ticket_type": type.toUpperCase(),
        "description": description,
        "event_id": eventId,
        "group_size": groupSize,
        "start_date": startDate.toUtc().toIso8601String(),
        "end_date": endDate.toUtc().toIso8601String(),
      };
      print(json.encode(_data));
      var res = await apiProvider.request(
          url: ApiUrls.EDITTICKET, method: Method.PUT, params: _data);
      if (res.data["status"]) {
        isloading(false);
        await fetchTickets(eventId);
        return true;
      } else {
        ToastUtils.showErrorToast(
          autoDismiss: true,
          context,
          'Error',
          res.data['message'],
        );
        return false;
      }
    } catch (e) {
      isloading(false);
      logger.e(e);
      ToastUtils.showErrorToast(
        context,
        'Error',
        "an error occured",
      );
      apiMessage("Error,Please try again");
      return false;
    } finally {
      isloading(false);
    }
  }
}

// class VerifyTicketController extends GetxController implements GetxService {
//   final pageController = PageController().obs;
//   RxBool isVerifying = false.obs;
//   final HttpService apiProvider = Get.find();
//   RxString prefix = "".obs;
//   RxBool isConfirming = false.obs;
//   // final selectedTickets = <Map<String, dynamic>>[].obs;
//   Future verifyTicket(int eventId, String code) async {
//     isVerifying(true);
//     try {
//       var response = await apiProvider.request(
//         url: ApiUrls.VERIFYTICKET,
//         method: Method.POST,
//         params: {"code": code, "event_id": "$eventId"},
//       );

//       if (response.data['status'] ?? false) {
//         Get.to(VerifyConfirm(
//           verify: VerifyTicketConfirm.fromJson(
//               response.data['data'] as Map<String, dynamic>),
//           eventId: eventId,
//           code: code,
//         ));
//       } else {
//         ToastUtils.showToast(
//             '${response.data['message'] ?? "Failed to verify ticket"}');
//       }
//     } catch (e) {
//       Get.snackbar('Error', 'An error occurred', backgroundColor: Colors.amber);
//       throw e;
//     } finally {
//       isVerifying(false);
//     }
//   }

//   Future verifyTicketConfirm(int eventId, String code, List<int> ids) async {
//     isConfirming(true);
//     try {
//       var response = await apiProvider.request(
//         url: ApiUrls.VERIFYTICKETCONFIRM,
//         method: Method.POST,
//         params: {
//           "code": code,
//           "event_id": "$eventId",
//           "transaction_ticket_ids": ids
//         },
//       );

//       if (response.data['status'] ?? false) {
//         Logger().d("prefix: ${prefix.value}");
//         // clear the prefix text
//         if (prefix.value.isNotEmpty) {
//           if (prefix.value.contains('-')) {
//             var split = prefix.value.split("-");
//             prefix("${split[0]}-");
//           }
//         }
//         Get.back();
//         Get.snackbar('Success', '${response.data['message'] ?? "Success"}',
//             backgroundColor: Colors.green);
//       } else {
//         Get.snackbar(
//             'Error', '${response.data['message'] ?? "Failed to verify ticket"}',
//             backgroundColor: Colors.red);
//       }
//     } catch (e) {
//       Get.snackbar('Error', ' ${e.toString()}', backgroundColor: Colors.amber);
//     } finally {
//       isConfirming(false);
//     }
//   }
// }
