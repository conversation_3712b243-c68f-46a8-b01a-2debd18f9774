import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/widgets/custom_international_phone_input.dart';
import 'package:mime/mime.dart';
import 'package:onekitty/controllers/events/create_event_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/events/categories_model.dart';
import 'package:onekitty/utils/my_text_field.dart';
import 'package:onekitty/utils/show_snackbar.dart';
import 'package:flutter_quill/flutter_quill.dart' as q;
import 'package:onekitty/main.dart' show isLight;

class EventDetails extends StatelessWidget {
  final TextEditingController eventTitle, emailAddress, phoneNumber;
  final q.QuillController eventDescription;
  // final TextEditingController eventDescription;
  final GlobalKey<FormState> formKey;

  EventDetails(
      {super.key,
      required this.eventTitle,
      required this.eventDescription,
      required this.emailAddress,
      required this.phoneNumber,
      required this.formKey});
  final _controller = Get.find<CreateEventController>();

  @override
  Widget build(BuildContext context) {
    _controller.bannerList.clear();
    return SingleChildScrollView(
      child: Form(
        key: formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MyTextFieldwValidator(
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Event title is required';
                  }
                  return null;
                },
                controller: eventTitle,
                title: 'Event Title',
                hint: 'e.g Kenya awards night'),
            SizedBox(height: 16.h),
            Text(
              'Event Description',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
            ),
            Padding(
              padding: const EdgeInsets.all(4.0),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.0),
                  border: Border.all(color: Colors.grey.shade400),
                ),
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),
                    q.QuillSimpleToolbar(
                      controller: eventDescription,
                      config: const q.QuillSimpleToolbarConfig(
                        multiRowsDisplay: false,
                      ),
                    ),
                    const SizedBox(height: 15),
                    q.QuillEditor.basic(
                      controller: eventDescription,
                      config: const q.QuillEditorConfig(
                        placeholder:
                            "Describe your event",

                        // readOnly: false,
                        autoFocus: false,
                        enableInteractiveSelection:
                            true, // Enable interactive selection to allow text editing
                      ),
                    ),
                    const SizedBox(height: 8),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16.h),
            MyTextFieldwValidator(
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Email address is required';
                  }
                  if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
                    return 'Enter a valid email address';
                  }

                  return null;
                },
                keyboardType: TextInputType.emailAddress,
                controller: emailAddress,
                title: 'Email Address',
                hint: 'e.g <EMAIL>'),
            SizedBox(height: 16.h),
            CustomInternationalPhoneInput(
              onInputChanged: (
                PhoneNumber number,
              ) {
                phoneNumber.text =
                    number.phoneNumber.toString().replaceAll("+", '');
                print(phoneNumber.text);
              },
              onInputValidated: (bool value) {},
              ignoreBlank: false,
              initialValue: PhoneNumber(
                  isoCode: 'KE',
                  dialCode: '+254',
                  phoneNumber: "+${phoneNumber.text}"),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Phone Number is required';
                }
                return null;
              },
              formatInput: true,
            ),
            SizedBox(height: 16.h),
            Text(
              'Category',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 8.h),
            GetX<CreateEventController>(
                init: CreateEventController(),
                initState: (state) async {
                  await state.controller?.getCategories();

                  for (CategoriesModel element
                      in state.controller?.categories ?? []) {
                    print("${element.title} ${element.id}");
                  }
                },
                builder: (controller) {
                  if (controller.isLoadingCategories.isTrue) {
                    return Container(
                      height: 55.h,
                      padding: const EdgeInsets.all(8),
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color:
                            isLight.value ? Colors.white70 : Colors.transparent,
                        border: Border.all(color: Colors.grey, width: 0.5),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('Select Category'),
                          CupertinoActivityIndicator()
                        ],
                      ),
                    );
                  } else if (controller.categories.isEmpty) {
                    return Container(
                      height: 55.h,
                      padding: const EdgeInsets.all(8),
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color:
                            isLight.value ? Colors.white70 : Colors.transparent,
                        border: Border.all(color: Colors.grey, width: 0.5),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: const Center(child: Text('No categories found')),
                    );
                  } else {
                    return DropdownButtonFormField<CategoriesModel>(
                      value: controller.selCategory?.value,
                      decoration: InputDecoration(
                        hintText: "Select Category",
                        filled: true,
                        fillColor:
                            isLight.value ? Colors.white70 : Colors.transparent,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.r),
                          borderSide: const BorderSide(
                            width: 0.5,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                      items: controller.categories.map((category) {
                        return DropdownMenuItem<CategoriesModel>(
                          value: category,
                          child: Text(category.title ?? ''),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          controller.category.value = value.id!;
                          controller.selCategory?.value = value;
                        }
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'Category is required';
                        }
                        return null;
                      },
                    );
                  }
                }),
            SizedBox(height: 16.h),
            Text(
              'Banner',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 8.h),
            GestureDetector(
              onTap: () async {
                if (!_controller.isUploading.value) {
                  final result = await FilePicker.platform.pickFiles(
                    allowMultiple: false,
                  );
                  if (result == null || result.files.isEmpty) {
                    _controller.isUploading(false);
                    ToastUtils.showToast('Nothing picked');
                    return;
                  }
                  final String mimeType =
                      lookupMimeType(result.files.first.path ?? '') ?? '';
                  if (!mimeType.startsWith('image/')) {
                    ToastUtils.showToast('Please select an image file');
                    _controller.isUploading(false);
                    return;
                  }

                  if (result.files.isNotEmpty) {
                    final file = result.xFiles.first.path;
                    _controller.bannerList.add({"name": file});
                    final url = await _controller.uploadFile(
                        path: file,
                        fileName:
                            "${DateTime.now().millisecondsSinceEpoch}_${file.split(RegExp(r'[/\\]')).last}");
                    _controller.eventMedia.add({'url': url, 'type': "image"});
                  } else {
                    showSnackbar(
                        context: context, label: 'Error : Nothing picked');
                  }
                } else {
                  showSnackbar(
                      context: context,
                      label:
                          'Please wait for the file to upload before uploading another!',
                      color: Colors.yellow);
                }
              },
              child: Container(
                height: 55.h,
                padding: const EdgeInsets.all(8),
                width: double.infinity,
                decoration: BoxDecoration(
                  color: isLight.value ? Colors.white70 : Colors.transparent,
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Upload Event Banner',
                      style: TextStyle(fontSize: 14.spMin),
                    ),
                    SizedBox(width: 8.w),
                    const Icon(Icons.upload_file),
                  ],
                ),
              ),
            ),
            SizedBox(
              height: 100,
              child: GetX<CreateEventController>(builder: (controller) {
                return ListView.builder(
                    shrinkWrap: true,
                    scrollDirection: Axis.horizontal,
                    itemCount: controller.bannerList.length,
                    itemBuilder: (context, index) {
                      return Obx(
                        () => Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              Image.file(
                                File("${controller.bannerList[index]['name']}"),
                                height: 80,
                                width: 80,
                                fit: BoxFit.cover,
                              ),
                              index == _controller.bannerList.length - 1
                                  ? _controller.isUploading.value
                                      ? const CircularProgressIndicator
                                          .adaptive()
                                      : Positioned(
                                          child: IconButton(
                                          icon:
                                              const Icon(Icons.delete_outline),
                                          onPressed: () {
                                            controller.bannerList
                                                .removeAt(index);
                                          },
                                        ))
                                  : Positioned(
                                      child: IconButton(
                                      icon: const Icon(Icons.delete_outline),
                                      onPressed: () {
                                        controller.bannerList.removeAt(index);
                                      },
                                    ))
                            ],
                          ),
                        ),
                      );
                    });
              }),
            ),
            SizedBox(height: 200.h)
          ],
        ),
      ),
    );
  }
}
