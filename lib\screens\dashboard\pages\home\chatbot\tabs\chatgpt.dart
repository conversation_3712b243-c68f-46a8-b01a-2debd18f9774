import 'package:chat_gpt_sdk/chat_gpt_sdk.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/config.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/message.dart';

class ChatGptPage extends StatefulWidget {
  const ChatGptPage({super.key});

  @override
  State<ChatGptPage> createState() => _ChatGptPageState();
}

class _ChatGptPageState extends State<ChatGptPage> {
  final TextEditingController _textController = TextEditingController();
  final FocusNode _textFieldFocus = FocusNode();
  ConfigController configController = Get.put(ConfigController());
  late OpenAI openAI;
  final List<Message> _messages = [];
  @override
  void initState() {
    openAI = OpenAI.instance.build(token: configController.gpt.value);
    super.initState();
  }

  void sendMessage() async {
    var text = _textController.text.trim();
    if (text.isEmpty) {
      return;
    }
    _textController.clear();
    setState(() {
      _messages.add(Message(isUser: true, text: text));
    });
    var res = await openAI.onCompletion(
        request: CompleteText(prompt: text, model: Babbage002Model()));

    if (res != null && res.choices.isNotEmpty) {
      setState(() {
        _messages.add(Message(isUser: false, text: res.choices.last.text));
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final textFieldDecoration = InputDecoration(
      contentPadding: const EdgeInsets.all(15),
      hintText: 'Enter a prompt...',
      border: OutlineInputBorder(
        borderRadius: const BorderRadius.all(
          Radius.circular(14),
        ),
        borderSide: BorderSide(
          color: Theme.of(context).colorScheme.secondary,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: const BorderRadius.all(
          Radius.circular(14),
        ),
        borderSide: BorderSide(
          color: Theme.of(context).colorScheme.secondary,
        ),
      ),
    );
    return Column(
      children: [
        Expanded(
            child: ListView.builder(
                itemCount: _messages.length,
                itemBuilder: (context, index) {
                  var message = _messages[index];
                  return Container(
                      decoration: BoxDecoration(
                        color: message.isUser
                            ? AppColors.blueButtonColor
                            : Theme.of(context).colorScheme.surfaceVariant,
                        borderRadius: BorderRadius.only(
                            topLeft: message.isUser
                                ? const Radius.circular(15)
                                : Radius.zero,
                            bottomLeft: const Radius.circular(15),
                            topRight: message.isUser
                                ? Radius.zero
                                : const Radius.circular(15),
                            bottomRight: const Radius.circular(15)),
                      ),
                      padding: const EdgeInsets.symmetric(
                        vertical: 15,
                        horizontal: 20,
                      ),
                      margin: const EdgeInsets.only(bottom: 8),
                      child: Text(
                        message.text,
                        style: context.dividerTextLarge
                            ?.copyWith(color: AppColors.slate),
                      ));
                })),
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 25,
            horizontal: 15,
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  autofocus: true,
                  focusNode: _textFieldFocus,
                  decoration: textFieldDecoration,
                  controller: _textController,
                  //onSubmitted: _sendChatMessage,
                ),
              ),
              const SizedBox.square(dimension: 15),
              IconButton(
                onPressed: () {
                  sendMessage();
                },
                icon: const Icon(
                  Icons.send,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
