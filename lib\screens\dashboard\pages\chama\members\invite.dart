import 'dart:async';
import 'dart:math';

import 'package:flutter_contacts/contact.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter_contacts/properties/phone.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/widgets/custom_international_phone_input.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/chama/chamaDto.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/widgets/contacts_controller.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/utils_exports.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class Invite extends ConsumerStatefulWidget {
  const Invite({super.key});

  @override
  _InviteState createState() => _InviteState();
}

class _InviteState extends ConsumerState<Invite> {
  final ChamaDataController dataController = Get.put(ChamaDataController());
  final ChamaController chamaController = Get.put(ChamaController());
  TextEditingController phoneController = TextEditingController();
  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  PhoneNumber num = PhoneNumber(isoCode: 'KE');
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  List<String> dropdownItems = [];

  final _streamController = StreamController<void>();
  Stream<void> get _stream => _streamController.stream;

  void startTimer() {
    Timer.periodic(const Duration(seconds: 1), (timer) {
      _streamController.add(null);
    });
  }

  void cancelTimer() {
    _streamController.close();
  }

  String invitePhone = "";

  Set<Contact> selectedContacts = <Contact>{};
  String selectedvalue = "MEMBER";
  @override
  void initState() {
    dropdownItems = chamaController.roles.map((role) => role.role).toList();

    startTimer();
    super.initState();
  }

  @override
  void dispose() {
    clearContacts();
    super.dispose();
  }

  void clearContacts() async {
    ref
        .read(selectContactControllerProvider.notifier)
        .removeAllSelectedContacts();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        //appBar: buildAppBar(context),
        body: SingleChildScrollView(
          child: Container(
            width: double.maxFinite,
            padding: EdgeInsets.all(10.w),
            child: Column(
              children: [
                const RowAppBar(),
                Text(dataController.chama.value.chama?.title ?? "",
                    style: CustomTextStyles.titleMediumBlack900),
                // Text("${chamaDataController.chama.value.membersCount} Members",
                // style: theme.textTheme.titleLarge),
                GetBuilder(
                  builder: (ChamaController chamaController) {
                    if (chamaController.isloadingChama.isTrue) {
                      return const Text("checking");
                    }

                    return Text(
                        "${chamaController.chamaMembers.length} Members",
                        style: theme.textTheme.titleLarge);
                  },
                ),
                SizedBox(height: 20.h),
                Text("Add Chama Members", style: theme.textTheme.titleLarge),
                SizedBox(height: 10.h), 
                _inputPhoneNumber(context),

                SizedBox(height: 20.h),

                StreamBuilder<void>(
                  stream: _stream,
                  builder: (context, snapshot) {
                    return buildInviteContacts(context);
                  },
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: Obx(
          () => CustomKtButton(
            isLoading: chamaController.isAdding.isTrue,
            width: 180.w,
            height: 50.h,
            btnText: "Add Members",
            onPress: () async {
              final selected = ref
                  .watch(selectContactControllerProvider.notifier)
                  .getSelectedContacts();
              try {
                final List<Member> members = [];
                for (var index = 0; index < selected.length; index++) {
                  final contact = selected[index];
                  final phoneNumber = contact.phones.first.normalizedNumber;

                  final order = chamaController.chamaMembers
                      .map((member) => member.receivingOrder ?? 0)
                      .reduce(max);
                  final member = Member(
                    phoneNumber: phoneNumber,
                    firstName: contact.name.first,
                    secondName: contact.name.last,
                    role: contact.name.prefix,
                    receivingOrder: order + index + 2,
                    status: "ACTIVE",
                  );
                  members.add(member);
                }

                final chamaMembers = MembersDto(
                    chamaId: dataController.singleChamaDts.value.id ?? 0,
                    members: members);

                final resp =
                    await chamaController.addMember(memebersDto: chamaMembers);
                if (resp) {
                  ToastUtils.showSuccessToast(
                    context,
                    chamaController.apiMessage.string,
                    "success",
                  );
                  clearContacts();
                  chamaController.getChamaMembers(
                      chamaId: dataController.singleChamaDts.value.id ?? 0,
                      sort: "LEADERS");
                  Get.back();
                } else {
                  ToastUtils.showErrorToast(
                    context,
                    chamaController.apiMessage.string,
                    "Error",
                  );
                }
              } catch (e) {
                // Handle error gracefully, show error toast, log error, etc.
                ToastUtils.showErrorToast(
                  context,
                  "An error occurred while adding member.",
                  "Error",
                );
              }
            },
            alignment: Alignment.bottomRight,
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      ),
    );
  }

  Widget _inputPhoneNumber(BuildContext context) {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          CustomInternationalPhoneInput(

              suffixIcon: InkWell(
                onTap: () {
                  Get.toNamed(
                    NavRoutes.selectcontacts,
                  );
                },
                child: CustomImageView(
                  imagePath: AssetUrl.addrbook,
                  height: 20.h,
                ),
              ),
            
            onInputChanged: (num) {
              setState(() {
                invitePhone = num.phoneNumber!;
              });
            },
            onInputValidated: (bool isValid) {},
             
            ignoreBlank: false,
             
            initialValue: num,
            textFieldController: phoneController,
            formatInput: true,
            keyboardType: const TextInputType.numberWithOptions(
                signed: true, decimal: true),
            inputBorder: const OutlineInputBorder(),
          ),
          SizedBox(
            height: 8.h,
          ),
          CustomElevatedButton(
              leftIcon: Container(
                margin: EdgeInsets.only(right: 1.w),
                child: CustomImageView(
                    imagePath: AssetUrl.imgPlus, height: 15.h, width: 15.w),
              ),
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  String phoneNumber = invitePhone;

                  Phone phone =
                      Phone(phoneNumber, normalizedNumber: phoneNumber);

                  Contact contact = Contact(
                    phones: [phone],
                  );
                  selectContact(contact, context);

                  setState(() {
                    phoneController.clear();
                  });
                }
              },
              height: 45.h,
              width: 100.w,
              text: "Add",
              buttonStyle: CustomButtonStyles.fillIndigR,
              buttonTextStyle: CustomTextStyles.titleSmallWhiteA700),
        ],
      ),
    );
  }

  Widget buildInviteContacts(BuildContext context) {
    final selectedContacts = ref
        .watch(selectContactControllerProvider.notifier)
        .getSelectedContacts();
    startTimer();
    // Check if selected contacts list is empty
    if (selectedContacts.isEmpty) {
      return CustomImageView(
        imagePath: AssetUrl.imgGroup13,
        height: 150.h,
        width: 254.w,
      );
    } else {
      return Container(
        height: 400.h,
        margin: EdgeInsets.only(left: 2.h),
        padding: EdgeInsets.symmetric(horizontal: 2.h, vertical: 17.h),
        decoration: AppDecoration.outlineGray
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder8),
        child: Column(
          children: [
            Expanded(
              child: ListView.builder(
                itemCount: ref
                    .watch(selectContactControllerProvider.notifier)
                    .getSelectedContacts()
                    .length,
                itemBuilder: (context, index) {
                  final selectedContact = ref
                      .watch(selectContactControllerProvider.notifier)
                      .getSelectedContacts()[index];

                  return Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadiusStyle.roundedBorder8),
                    child: Column(
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomImageView(
                              imagePath: AssetUrl.dotSix,
                              height: 25.h,
                              width: 25.w,
                              margin: EdgeInsets.only(right: 3.h),
                            ),
                            Opacity(
                              opacity: 0.5,
                              child: Padding(
                                padding: EdgeInsets.only(top: 6.h, bottom: 8.h),
                                child: Text(
                                  "${index + 1}",
                                  style: theme.textTheme.titleSmall!.copyWith( 
                                  ),
                                ),
                              ),
                            ),
                            CustomImageView(
                              imagePath: AssetUrl.imgPerson,
                              height: 25.h,
                              width: 25.w,
                              margin: EdgeInsets.only(left: 3.h),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: EdgeInsets.only(
                                      left: 6.h, top: 1.h, bottom: 1.h),
                                  child: Text(
                                    "${selectedContact.name.first} ${selectedContact.name.last}",
                                    overflow: TextOverflow.ellipsis,
                                    style: CustomTextStyles.titleSmallGray90001 
                                  ),
                                ),
                                Padding(
                                  padding: EdgeInsets.only(
                                      left: 6.h, top: 1.h, bottom: 1.h),
                                  child: Text(
                                    selectedContact.phones.first.number,
                                    style: CustomTextStyles.titleSmallGray90001
                                        
                                  ),
                                ),
                              ],
                            ),
                            const Spacer(),
                            selectedContact.name.prefix == "CHAIRPERSON"
                                ? CustomImageView(
                                    imagePath: AssetUrl.crownsv,
                                    height: 18.h,
                                    width: 18.w,
                                    margin: EdgeInsets.symmetric(vertical: 9.h),
                                  )
                                : const SizedBox.shrink(),
                            Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: 10.h, horizontal: 2.h),
                              child: Text(selectedContact.name.prefix),
                            ),
                            InkWell(
                              onTap: () {
                                ref
                                    .read(selectContactControllerProvider
                                        .notifier)
                                    .removeSelectedContact(selectedContact);
                                setState(() {});
                              },
                              child: CustomImageView(
                                imagePath: AssetUrl.imgIconoirCancel,
                                height: 18.h,
                                width: 18.w,
                                margin: EdgeInsets.symmetric(
                                    vertical: 9.h, horizontal: 5.h),
                              ),
                            ),
                            IconButton(
                              icon: const Icon(
                                Icons.edit,
                                color: AppColors.blueButtonColor,
                              ),
                              padding: EdgeInsets.symmetric(vertical: 10.h),
                              onPressed: () {
                                _chamaOptionsDialog(
                                    context, index, selectedContact);
                              },
                            )
                          ],
                        ),
                        SizedBox(height: 2.h),
                        const Divider()
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      );
    }
  }

  void selectContact(
    Contact selectedContact,
    BuildContext context,
  ) {
    if (!selectedContacts.contains(selectedContact)) {
      selectedContact.name.prefix = "MEMBER";
      ref
          .read(selectContactControllerProvider.notifier)
          .selectContact(selectedContact, context);
      selectedContacts.add(selectedContact);
      setState(() {});
    } else {
      ToastUtils.showInfoToast(context, "Contact Already Selected",
          "Check"); // Show SnackBar if contact is already selected
    }
  }

  void _chamaOptionsDialog(
      BuildContext context, int index, Contact selectedContact) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        // Local state for the selected status within the dialog

        firstNameController.text = selectedContact.name.first;
        lastNameController.text = selectedContact.name.last;
        selectedvalue = selectedContact.name.prefix;
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Align(
              alignment: Alignment.centerRight,
              child: AlertDialog(
                title: const Text(
                  "Update Member Details",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                content: SizedBox(
                  height: 200.h,
                  child: Column(
                    children: [
                      CustomTextField(
                        controller: firstNameController,
                        labelText: "Enter First Name",
                      ),
                      CustomTextField(
                        controller: lastNameController,
                        labelText: "Enter Last Name",
                      ),
                      SizedBox(
                        height: 8.h,
                      ),
                      DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: "Select Role",
                          fillColor: Colors.blueAccent.withOpacity(0.1),
                        ),
                        isExpanded: true,
                        items: dropdownItems
                            .map(
                              (String item) => DropdownMenuItem<String>(
                                value: item,
                                child: Text(
                                  item,
                                  style: const TextStyle(
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            )
                            .toList(),
                        value: selectedvalue,
                        onChanged: (String? value) {
                          setState(() {
                            selectedvalue = value!;
                          });
                        },
                      ),
                    ],
                  ),
                ),
                actions: [
                  CustomElevatedButton(
                    text: "Save",
                    onPressed: () {
                      try {
                        updateContact(
                          selectedContact.phones.first.normalizedNumber,
                          firstNameController.text,
                          lastNameController.text,
                          selectedvalue,
                        );
                      } catch (e) {}

                      Navigator.of(context).pop(); // Close the dialog
                    },
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void updateContact(
    String id,
    String newFirstName,
    String newLastName,
    String role,
  ) {
    setState(() {
      final selectedContact = ref
          .watch(selectContactControllerProvider.notifier)
          .getSelectedContacts();

      //access and update the contact list
      final contact = selectedContact
          .firstWhere((contact) => contact.phones.first.normalizedNumber == id);
      contact.name.first = newFirstName;
      contact.name.last = newLastName;
      contact.name.prefix = role;
    });
  }
}
