// To parse this JSON data, do
//
//     final kittyUser = kittyUserFromJson(jsonString);

import 'dart:convert';


KittyUser kittyUserFromJson(String str) => KittyUser.fromJson(json.decode(str));

String kittyUserToJson(KittyUser data) => json.encode(data.toJson());

class KittyUser {
  Kitty? kitty;
  String? kittyStatus;
  String? kittBeneficiaryChannel;
  String? kittyType;
  int? volumes;

  KittyUser(
      {this.kitty,
      this.kittyStatus,
      this.kittBeneficiaryChannel,
      this.kittyType,
      this.volumes});

  factory KittyUser.fromJson(Map<String, dynamic> json) => KittyUser(
      kitty: json["kitty"] == null ? null : Kitty.fromJson(json["kitty"]),
      kittyStatus: json["kitty_status"],
      kittBeneficiaryChannel: json["kitt_beneficiary_channel"],
      kittyType: json["kitty_type"],
      volumes: json["volumes"]);

  Map<String, dynamic> toJson() => {
        "kitty": kitty?.toJson(),
        "kitty_status": kittyStatus,
        "kitt_beneficiary_channel": kittBeneficiaryChannel,
        "kitty_type": kittyType,
        "volumes": volumes
      };
}

class Kitty {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  String? title;
  String? description;
  String? beneficiaryAccount;
  String? beneficiaryAccountRef;
  String? beneficiaryChannel;
  String? beneficiaryPhoneNumber;
  DateTime? endDate;
  dynamic availableBalance;
  dynamic balance;
  dynamic limit;
  int? status;
  int? refererMerchantCode;
  String? phoneNumber;
  int? kittyType;
  int? userId;
  int? settlementType;

  Kitty({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.description,
    this.beneficiaryAccount,
    this.beneficiaryAccountRef,
    this.beneficiaryChannel,
    this.beneficiaryPhoneNumber,
    this.endDate,
    this.availableBalance,
    this.balance,
    this.limit,
    this.status,
    this.refererMerchantCode,
    this.phoneNumber,
    this.kittyType,
    this.settlementType,
    this.userId,
  });

  factory Kitty.fromJson(Map<String, dynamic> json) {
    return Kitty(
        id: json["ID"],
        createdAt: json["CreatedAt"] == null
            ? null
            : DateTime.parse(json["CreatedAt"]),
        updatedAt: json["UpdatedAt"] == null
            ? null
            : DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        title: json["title"],
        description: json["description"],
        beneficiaryAccount: json["beneficiary_account"],
        beneficiaryAccountRef: json["beneficiary_account_ref"],
        beneficiaryChannel: json["beneficiary_channel"],
        beneficiaryPhoneNumber: json["beneficiary_phone_number"],
        endDate:
            json["end_date"] == null ? null : DateTime.parse(json["end_date"]),
        availableBalance: json["available_balance"],
        balance: json["balance"],
        limit: json["limit"],
        status: json["status"],
        refererMerchantCode: json["referer_merchant_code"],
        phoneNumber: json["phone_number"] ?? "",
        kittyType: json["kitty_type"],
        userId: json["UserID"],
        settlementType: json["settlement_type"]);
  }

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "title": title,
        "description": description,
        "beneficiary_account": beneficiaryAccount,
        "beneficiary_account_ref": beneficiaryAccountRef,
        "beneficiary_channel": beneficiaryChannel,
        "beneficiary_phone_number": beneficiaryPhoneNumber,
        "end_date": endDate?.toIso8601String(),
        "available_balance": availableBalance,
        "balance": balance,
        "limit": limit,
        "status": status,
        "referer_merchant_code": refererMerchantCode,
        "phone_number": phoneNumber,
        "kitty_type": kittyType,
        "UserID": userId,
        "settlement_type":settlementType
      };
}
