import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/widgets/custom_international_phone_input.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/controllers/events/events_controller.dart';
import 'package:onekitty/controllers/events/transfer_page_controller.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:onekitty/utils/show_cached_network_image.dart';
import 'package:onekitty/models/auth/payments_channels.dart';
import 'package:onekitty/utils/my_text_field.dart';
import 'package:onekitty/utils/my_button.dart';

import '../../../onboarding/passwd_req_screen.dart';

class TransferScreen extends StatefulWidget {
  final int kittyId;
  const TransferScreen({
    super.key,
    required this.kittyId,
  });

  @override
  _TransferScreenState createState() => _TransferScreenState();
}

class _TransferScreenState extends State<TransferScreen> {
  final controller = Get.put(TransferPageController());
  final amountController = TextEditingController();
  final phone = TextEditingController();
  final reason = TextEditingController();
  final paybill = TextEditingController();
  final accountNumber = TextEditingController();
  final tillNumber = TextEditingController();
  final bankAccount = TextEditingController();
  final formKey = GlobalKey<FormState>();
  PhoneNumber number = PhoneNumber(isoCode: 'KE', dialCode: '+254');
  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context);
    final pageController = PageController(initialPage: controller.page.value);

    return Obx(
      () => Scaffold(
        appBar: AppBar(
          centerTitle: true,
          title: const Text("Transfer"),
        ),
        bottomNavigationBar: Obx(
          () => Padding(
            padding: EdgeInsets.only(bottom: 13.sp, left: 9, right: 9),
            child: MyButton(
              showLoading: controller.isTransfering.value,
              onClick: () {
                if (phone.text.isEmpty) {
                  phone.text =
                      Eventcontroller().getLocalUser()?.phoneNumber ?? "";
                }
                var phoneNumber =
                    "${number.dialCode}${phone.text.substring(phone.text.length - 9)}"
                        .replaceAll("+", '');
                if (controller.page.value != 0) {
                  controller.selectedProvider("M-PESA");
                }

                return formKey.currentState!.validate()
                    ? controller.transferRequest(
                        context,
                        kittyId: widget.kittyId,
                        amount: int.parse(amountController.text),
                        accNo: accountNumber.text,
                        till: tillNumber.text,
                        paybill: paybill.text,
                        phoneNumber: phoneNumber,
                        bankAccount: bankAccount.text,
                        transferMode: controller.page.value == 0
                            ? "WALLET"
                            : controller.page.value == 1
                                ? "PAYBILL"
                                : controller.page.value == 2
                                    ? "TILL"
                                    : controller.page.value == 3
                                        ? "BANK"
                                        : "",
                        reason: reason.text,
                      )
                    : Get.snackbar('Error', 'Please check phone number',
                        backgroundColor: Colors.red);
              },
              label: 'Transfer',
            ),
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Make a Transfer',
                    style: TextStyle(
                      fontSize: 22.spMin,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Make transactions with ease',
                    style: TextStyle(
                      fontSize: 16.spMin,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  MyTextFieldwValidator(
                      keyboardType: TextInputType.number,
                      controller: amountController,
                      title: 'Enter amount'),
                  SizedBox(height: 16.h),
                  Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 6, horizontal: 8),
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        color: primaryColor.withOpacity(0.15),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () => {
                                controller.page.value = 0,
                                pageController.jumpToPage(controller.page.value)
                              },
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(7.5),
                                  color: controller.page.value == 0
                                      ? Colors.white
                                      : null,
                                ),
                                child: Text('Mobile Money',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14.spMin,
                                      color: controller.page.value == 0
                                          ? primaryColor
                                          : null,
                                    )),
                              ),
                            ),
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () => {
                                controller.page.value = 1,
                                pageController.jumpToPage(controller.page.value)
                              },
                              child: Container(
                                alignment: Alignment.center,
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(7.5),
                                  color: controller.page.value == 1
                                      ? Colors.white
                                      : null,
                                ),
                                child: Text('Paybill',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14.spMin,
                                      color: controller.page.value == 1
                                          ? primaryColor
                                          : null,
                                    )),
                              ),
                            ),
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () => {
                                controller.page.value = 2,
                                pageController.jumpToPage(controller.page.value)
                              },
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(7.5),
                                  color: controller.page.value == 2
                                      ? Colors.white
                                      : null,
                                ),
                                child: Text('Till Number',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14.spMin,
                                      color: controller.page.value == 2
                                          ? primaryColor
                                          : null,
                                    )),
                              ),
                            ),
                          ), 
                          Expanded(
                            child: GestureDetector(
                              onTap: () => {
                                controller.page.value = 3,
                                pageController.jumpToPage(controller.page.value)
                              },
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(7.5),
                                  color: controller.page.value == 3
                                      ? Colors.white
                                      : null,
                                ),
                                child: Text('Bank',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14.spMin,
                                      color: controller.page.value == 3
                                          ? primaryColor
                                          : null,
                                    )),
                              ),
                            ),
                          ),
                        ],
                      )),
                  const SizedBox(height: 16),
                  SizedBox(
                    height: 230.h,
                    width: 520,
                    child: PageView(
                      controller: pageController,
                      onPageChanged: (page) => controller.page(page),
                      children: [
                        Column(
                          children: [
                            Obx(() => Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: [
                                    GestureDetector(
                                      onTap: () {
                                        controller.selectedProvider.value =
                                            'M-PESA';
                                      },
                                      child: Column(
                                        children: [
                                          Image.asset(
                                            'assets/images/mpesa.png',
                                            width: 50.w,
                                            height: 50.h,
                                          ),
                                          Radio(
                                            value: 'M-PESA',
                                            groupValue: controller
                                                .selectedProvider.value,
                                            onChanged: (value) {
                                              controller.selectedProvider
                                                  .value = value.toString();
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                    GestureDetector(
                                      onTap: () {
                                        controller.selectedProvider.value =
                                            'SasaPay';
                                      },
                                      child: Column(
                                        children: [
                                          Image.asset(
                                            'assets/images/sasapay.png',
                                            width: 50.w,
                                            height: 50.h,
                                          ),
                                          Radio(
                                            value: 'SasaPay',
                                            groupValue: controller
                                                .selectedProvider.value,
                                            onChanged: (value) {
                                              controller.selectedProvider
                                                  .value = value.toString();
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                    GestureDetector(
                                      onTap: () {
                                        controller.selectedProvider.value =
                                            'AirtelMoney';
                                      },
                                      child: Column(
                                        children: [
                                          Image.asset(
                                            'assets/images/airtelmoney.png',
                                            width: 50.w,
                                            height: 50.h,
                                          ),
                                          Radio(
                                            value: 'AirtelMoney',
                                            groupValue: controller
                                                .selectedProvider.value,
                                            onChanged: (value) {
                                              controller.selectedProvider
                                                  .value = value.toString();
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                )),
                            const SizedBox(height: 16),
                            Align(
                              alignment: Alignment.topLeft,
                              child: Text(
                                'Enter beneficiary phone number',
                                style: TextStyle(
                                    fontSize: 12.spMin,
                                    color: Colors.black,
                                    fontWeight: FontWeight.w600),
                              ),
                            ),
                            const SizedBox(
                              height: 10,
                            ),
                            CustomInternationalPhoneInput(
  onInputChanged: (PhoneNumber no,
) {
                                number = no;
                                phone.text = no.phoneNumber
                                    .toString()
                                    .replaceAll("+", '');
                              },
                              onInputValidated: (bool value) {},
                              ignoreBlank: false,
                               
                              initialValue: number,
                              validator: (value) {
                                if (value == null ||
                                    value.isEmpty ||
                                    value.length < 9) {
                                  return 'Phone Number is required';
                                }
                                return null;
                              },
                              // textFieldController: phoneNumber,
                              formatInput: true,                              
                            ),
                          ],
                        ),
                        Column(
                          children: [
                            MyTextFieldwValidator(
                              keyboardType: TextInputType.number,
                              controller: paybill,
                              title: 'Mpesa Paybill',
                            ),
                            SizedBox(
                              height: 20.h,
                            ),
                            MyTextFieldwValidator(
                              keyboardType: TextInputType.number,
                              controller: accountNumber,
                              title: 'Account Number',
                            )
                          ],
                        ),
                        Column(
                          children: [
                            MyTextFieldwValidator(
                              controller: tillNumber,
                              keyboardType: TextInputType.number,
                              title: 'Mpesa Till number',
                            ),
                          ],
                        ), 

                        Obx(
                          () => Column(
                            children: [
                              DropdownButton<PaymentChannels?>(
                                value: controller.selectedBank.value,
                                hint: const Text('Select Bank'),
                                items: Get.find<GlobalControllers>()
                                    .paymentChannels
                                    .where((e) => e.category == Category.BANK)
                                    .map((e) =>
                                        DropdownMenuItem<PaymentChannels?>(
                                          value: e,
                                          child: Row(
                                            children: [
                                              SizedBox(
                                                height: 30,
                                                width: 30,
                                                child: ShowCachedNetworkImage(
                                                  errorWidget: const Icon(
                                                      Icons.account_balance),
                                                  imageurl: e.imageUrl,
                                                  height: 30,
                                                  width: 30,
                                                ),
                                              ),
                                              Text(e.name),
                                            ],
                                          ),
                                        ))
                                    .toList(),
                                onChanged: (newValue) {
                                  controller.selectedBank.value = newValue;
                                  controller.selectedProvider.value = 'BANK';
                                },
                              ),
                              if (controller.selectedBank.value != null)
                                MyTextFieldwValidator(
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Please enter your bank account number';
                                    }

                                    return null;
                                  },
                                  controller: bankAccount,
                                  title: 'Bank Account',
                                  onChanged: (val) {
                                    controller.selectedProvider.value = 'BANK';
                                  },
                                )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 8.h),
                  MyTextFieldwValidator(
                    controller: reason,
                    title: 'Enter reason for transaction',
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class TransferConfirmPage extends StatelessWidget {
  final dynamic res, selectedProvider;
  final int kittyId;
  final int amount;
  final String receiverName;
  final String receiverAccRef;
  final String reason, transferMode;
  const TransferConfirmPage({
    super.key,
    this.res,
    this.selectedProvider,
    required this.kittyId,
    required this.amount,
    required this.receiverName,
    required this.receiverAccRef,
    required this.reason,
    required this.transferMode,
  });

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(TransferPageController());
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Transfer Confirmation'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Confirm to transfer ${FormattedCurrency().getFormattedCurrency(res.data['data']['amount_received'])} to ${res.data['data']['beneficiary_account_ref']} - ${res.data['data']['beneficiary_name']}',
              style: const TextStyle(
                  fontWeight: FontWeight.w600, fontSize: 18), // Enlarged text
            ),
            const SizedBox(height: 16),
            Text('Amount : KES ${res.data['data']['amount_received']}',
                style: const TextStyle(fontSize: 18)), // Enlarged text
            Text(
                'CHARGES: KES ${res.data['data']['charges'].truncateToDouble()}',
                style: const TextStyle(fontSize: 18)), // Enlarged text
            Text(
                'BALANCE : ${FormattedCurrency().getFormattedCurrency(res.data['data']['kitty_balance'].truncateToDouble())}',
                style: const TextStyle(fontSize: 18)), // Enlarged text
            Text(
                'ACCOUNT NAME: ${res.data['data']['beneficiary_name'] ?? 'N/A'}',
                style: const TextStyle(fontSize: 18)), // Enlarged text
            Text(
                'RECEIVER ACCOUNT : ${res.data['data']['beneficiary_account']}',
                style: const TextStyle(fontSize: 18)), // Enlarged text
            const SizedBox(height: 24),
            const Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                MyButton(
                  outlined: true,
                  width: 170.w,
                  label: 'Cancel',
                  onClick: () {
                    Get.back();
                  },
                ),
                Obx(
                  () => MyButton(
                    showLoading: controller.isConfirmingTransfer.value,
                    width: 170.w,
                    label: 'Confirm',
                    onClick: () async {
                      var isAuthenticated = await Get.to(
                          () => AuthPasswdScreen(),
                          arguments: [false]);

                      if (isAuthenticated != null && isAuthenticated == true) {
                        final eventController =
                            Get.find<Eventcontroller>().getLocalUser();
                        controller.transferRequestConfirm(context, {
                          "amount": res.data['data']['amount_received'],
                          "user_id": eventController?.id,
                          "kitty_id": kittyId,
                          "channel_code": selectedProvider.value == "M-PESA"
                              ? 63902
                              : selectedProvider.value == "SasaPay"
                                  ? 0
                                  : selectedProvider.value == "AirtelMoney"
                                      ? 63903
                                      : selectedProvider.value == "Card"
                                          ? 55
                                          : selectedProvider.value == "Tkash"
                                              ? 63907
                                              : null,
                          "recipient_account_number": res.data['data']
                              ['beneficiary_account'],
                          "recipient_account_ref": res.data['data']
                              ['beneficiary_account_ref'],
                          "reason": reason,
                          "transfer_mode": transferMode,
                          "latitude": eventController?.latitude,
                          "longitude": eventController?.longitude,
                          "device_id": "",
                          "device_model": ""
                        }).whenComplete(() {
                          Navigator.pop(context);
                          Navigator.pop(context);
                        });
                      } else {
                        Logger().w("user is not authenticated");
                      }
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: 60.h),
          ],
        ),
      ),
    );
  }
}
