import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/events/view_single_event.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/screens/dashboard/pages/events/view_single_event_viewer.dart';
import 'package:onekitty/utils/date_formatter.dart';
import 'package:onekitty/utils/my_quill_editor.dart' show QuillEditorShortWidget;

// import 'package:onekitty/utils/my_quill_editor.dart';
import 'package:onekitty/utils/show_cached_network_image.dart';
import 'package:onekitty/main.dart' show isLight; 
import 'asset_urls.dart';
import 'event_details.dart';

// Custom page route for smoother hero animations
class HeroPageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;
  
  HeroPageRoute({required this.page})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: const Duration(milliseconds: 600),
          reverseTransitionDuration: const Duration(milliseconds: 500),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            // Fade animation
            var fadeAnimation = CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOut,
            );
            
            // Scale animation
            var scaleAnimation = Tween<double>(
              begin: 0.95,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutCubic,
            ));
            
            return FadeTransition(
              opacity: fadeAnimation,
              child: ScaleTransition(
                scale: scaleAnimation,
                child: child,
              ),
            );
          },
        );
}

class EventCards extends StatelessWidget {
  final bool? organizer;
  final Event event;
  const EventCards({super.key, required this.event, this.organizer});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Get.put(ViewSingleEventController()).event(event);
        Navigator.push(
            context,
            HeroPageRoute(
              page: const ViewSingleEventViewer(),
            ));
      },
      child: Card(
        elevation: 4,
        margin: const EdgeInsets.all(8),
        color: isLight.value ? Colors.white : const Color(0xff26262e), 
        child: Padding(
          padding: EdgeInsets.only(
            left: 6.spMin,
            right: 6.spMin,
            top: 6.spMin,
            bottom: 10.spMin,
          ),
          child: Column(
            children: [
              Hero(
                tag: 'image:${event.id}',
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxHeight: 300.h,
                  ),
                  child: AdaptiveCachedNetworkImage(
                    imageUrl:
                        event.eventMedia == null || event.eventMedia!.isEmpty
                            ? AssetUrl.onekittyBannnerUrl
                            : event.eventMedia![0].url,
                    initialWidth: 390.w,
                    borderRadius: 8.r,
                    initialHeight: 300.h,
                  ),
                ),
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Material(
                      color: Colors.transparent,
                      textStyle: TextStyle(
                        color: Colors.grey.shade700,
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Hero(
                              tag: 'text:${event.id}',
                              child: Material(
                                color: Colors.transparent,
                                child: Text(event.title,
                                    maxLines: 2,
                                    style:   TextStyle(
                                        color: isLight.value ? Colors.black : Colors.white70,
                                        fontWeight: FontWeight.w700,
                                        fontSize: 20)),
                              ),
                            ),
                          ),
                          EventDetailsWidget(
                              id: "location${event.id}",
                              label: "${event.locationTip} - ${event.venue}",
                              image: 'assets/images/icons/location.png',
                              icon: Icons.location_on_outlined),
                          EventDetailsWidget(
                              id: "date${event.id}",
                              image: 'assets/images/icons/calendar.png',
                              label:
                                  '${formatDate("${event.startDate?.toLocal()}")}, ${formatTime("${event.startDate?.toLocal()}")}',
                              icon: Icons.calendar_month),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Align(
                  alignment: Alignment.topLeft,
                  child: QuillEditorShortWidget(
                    maxLines: 2,
                    text: event.description,
                    tag: 'event_${event.id}', // Add unique tag
                  )),
            ],
          ),
        ),
      ),
    );
  }
}
/*?
class RecommendedCardsWidget extends StatelessWidget {
  const RecommendedCardsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15), // Rounded corners
      ),
      elevation: 5,
      child: Padding(
        padding: const EdgeInsets.all(6.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: Image.network(
                    'https://onewayeventproductions.com/wp-content/uploads/2020/01/AdobeStock_290777302.jpeg',
                    width: 120,
                    height: 120,
                    fit: BoxFit.cover,
                  ),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Kenya Awards',
                          style: TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.w700,
                              fontSize: 20)),
                      EventDetailsWidget(
                          label: 'Lavington, Nairobi',
                          icon: Icons.location_on_outlined),
                      EventDetailsWidget(
                          label: '14 Feb 2024, 8:00AM',
                          icon: Icons.access_time_rounded),
                      EventDetailsWidget(
                          label: '122 Members', icon: Icons.people_outline)
                    ],
                  ),
                ),
              ],
            ),
            // SizedBox(height: 12),s
            const Text(
              'Pride of Kenya Awards Galla night Event will be hosted by Tony Mwirigi. Be sure to turn up and enjoy.',
              style: TextStyle(fontSize: 16),
            ),
            // SizedBox(height: 12),
            Row(
              children: [
                const AttendeesWidget(
                  //TODO recommended widget
                  count: 0,
                  padding: 4,
                ),
                const Spacer(),
                OutlinedButton(
                    onPressed: () {},
                    child: const Padding(
                      padding: EdgeInsets.all(14.0),
                      child: Text('View'),
                    ))
              ],
            )
          ],
        ),
      ),
    );
  }
}
  1,2,3,4
*/