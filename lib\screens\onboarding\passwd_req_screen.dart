import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:local_auth/local_auth.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/controllers/auth_controller.dart';
import 'package:onekitty/helpers/loader_easy.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/screens/onboarding/blocked_page.dart';
import 'package:onekitty/screens/onboarding/maintainance_page.dart';
import 'package:onekitty/screens/onboarding/login_screen.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/services/auth_manager.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:encrypt/encrypt.dart' as crypt;
import 'package:onekitty/utils/lottie/my_lottie.dart';

import '../../utils/utils_exports.dart'; 
import 'pinput.dart';
import 'sign_up.dart';
import 'updateKYC/views/kyc_home.dart';

// ignore: must_be_immutable
class AuthPasswdScreen extends StatefulWidget {
  final bool? isForgot;

  // ignore: prefer_const_constructors_in_immutables
  AuthPasswdScreen({
    super.key,
    this.isForgot,
  });

  @override
  State<AuthPasswdScreen> createState() => _AuthPasswdScreenState();
}

class _AuthPasswdScreenState extends State<AuthPasswdScreen> {
  final TextEditingController passwordController = TextEditingController();
  final AuthenticationController authenticationController = Get.find();

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool hidePassword = true;

  bool isFromStartPage = true;

  final LocalAuthentication auth = LocalAuthentication();
  Logger logger = Get.find();
  GetStorage box = Get.find();
  late bool allowsBiometrics;

  Future<void> _getAvailableBiometrics() async {
    List<BiometricType> availableBiometrics = [];
    late bool canCheckBiometrics;
    try {
      canCheckBiometrics = await auth.canCheckBiometrics;

      if (canCheckBiometrics) {
        availableBiometrics = await auth.getAvailableBiometrics();
        if (kDebugMode) {}
        if (GetStorage().read(CacheKeys.allowsBiometric) != null) {
          bool allowsBio = GetStorage().read(CacheKeys.allowsBiometric);
          if (allowsBio) {
            _authenticateWithBiometrics();
          }
        }
      }
    } on PlatformException catch (e) {
      availableBiometrics = <BiometricType>[];
      if (kDebugMode) {
        throw e.toString();
      }
    }
    if (!mounted) {
      return;
    }

    setState(() {});
  }

  Future<void> _authenticateWithBiometrics() async {
    bool authenticated = false;
    try {
      if (!mounted) return;
      setState(() {});
      
      // Check if biometrics are available before attempting authentication
      bool canCheckBiometrics = await auth.canCheckBiometrics;
      List<BiometricType> availableBiometrics = [];
      
      if (canCheckBiometrics) {
        availableBiometrics = await auth.getAvailableBiometrics();
        
        if (availableBiometrics.isEmpty) {
          if (mounted) {
            debugPrint( "No biometric authentication methods available");
          }
          Loader.dismiss();
          return;
        }
        
        authenticated = await auth.authenticate(
            localizedReason: 'Scan your fingerprint to authenticate',
            options: const AuthenticationOptions(
                useErrorDialogs: true, stickyAuth: true, biometricOnly: true));

        if (mounted) {
          setState(() {});
        }
      } else {
        if (mounted) {
          Snack.showInfo(message1: "Biometric authentication not available on this device");
        }
        Loader.dismiss();
        return;
      }
    } on PlatformException catch (e) {
      logger.e(e);
      Loader.dismiss();
      
      // Handle specific error cases
      if (e.code == 'NotAvailable') {
        if (mounted) {
          Snack.showInfo(message1: "Biometric credentials not set up on this device");
        }
      } else if (e.code == 'NotEnrolled') {
        if (mounted) {
          Snack.showInfo(message1: "No biometric credentials enrolled on this device");
        }
      } else {
        if (mounted) {
          Snack.showInfo(message1: "Biometric authentication failed: ${e.message}");
        }
      }
      return;
    }

    if (authenticated) {
      Loader.showLoader();
      var deKey = box.read(CacheKeys.encryption);
      var encrypted = box.read(CacheKeys.encrypted);
      var aes = box.read(CacheKeys.iv);

      var decryptKey = crypt.Key.fromUtf8(deKey ?? '');
      final encrypter = crypt.Encrypter(crypt.AES(decryptKey));

      final iv = crypt.IV.fromBase64(aes);
      final decrypted = encrypter.decrypt64(encrypted, iv: iv);

      await handleAuth(decrypted);
      Loader.dismiss();
    }
  }

  @override
  void initState() {
    if (Get.arguments != null) {
      isFromStartPage = Get.arguments[0];

      setState(() {});
    }
    allowsBiometrics = box.read(CacheKeys.allowsBiometric) ?? false;
    _getAvailableBiometrics();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: _buildAuthenticationAppBar(context),
        body: Stack(
          children: [
            const MySnowFall(),
            SizedBox(
              child: SingleChildScrollView(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom,
                ),
                child: Form(
                  key: _formKey,
                  child: Container(
                    width: double.maxFinite,
                    padding: EdgeInsets.symmetric(
                      horizontal: 25.w,
                      vertical: 20.h,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Align(
                          alignment: Alignment.center,
                          child: CustomImageView(
                            imagePath: AssetUrl.logo7,
                            height: 200.h,
                            width: 200.w,
                            fit: BoxFit.cover,
                            // margin: EdgeInsets.only(left: 60.w),
                          ),
                        ),
                        SizedBox(height: 1.h),
                        Padding(
                          padding: EdgeInsets.only(left: 47.w),
                          child: Align(
                            alignment: Alignment.center,
                            child: Text(
                              "Enter your password to continue",
                              style: CustomTextStyles.titleMediumBlack900,
                            ),
                          ),
                        ),
                        SizedBox(height: 12.h),
                        Padding(
                          padding: EdgeInsets.only(
                            left: 16.h,
                            right: 2.h,
                          ),
                          child: CustomTextField(
                            prefixIcon: Icons.lock,
                            controller: passwordController,
                            obscureText: hidePassword,
                            
                            suffixIcon: GestureDetector(
                              onTap: () {
                                setState(() {
                                  hidePassword = !hidePassword;
                                });
                              },
                              child: hidePassword
                                  ? const Icon(
                                      Icons.remove_red_eye,
                                    )
                                  : const Icon(
                                    
                                      Icons.visibility_off,
                                    ),
                            ),
                            hintText: "Password",
                            labelText: "Password",
                            isRequired: true,
                            validator: (value) {
                              if (value!.isEmpty) {
                                return "Password required";
                              } else {
                                return null;
                              }
                            },
                          ),
                        ),
                        SizedBox(height: 14.h),
                        Align(
                          alignment: Alignment.centerRight,
                          child: InkWell(
                            onTap: () {
                              // Get.to(() => SucessPage(text: "tes"));
                              // return;

                              AuthenticationManager authenticationManager =
                                  Get.find();
                              authenticationManager.logOut();
                              Get.offAll(
                                () => const LoginScreen(),
                                transition: Transition.leftToRightWithFade,
                              );
                            },
                            child: Text("Click here to log out",
                                style: TextStyle(
                                    fontSize: 15.h,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87)),
                          ),
                        ),
                        SizedBox(height: 20.h),
                        Obx(
                          () => Center(
                            child: CustomKtButton(
                              isLoading: authenticationController
                                  .isLoginloading.isTrue,
                              onPress: () async {
                                if (_formKey.currentState!.validate()) {
                                  await handleAuth(
                                    passwordController.text,
                                  );
                                }
                                print(widget.isForgot);
                              },
                              width: 250.w,
                              btnText: 'Continue',
                              alignment: Alignment.center,
                            ),
                          ),
                        ),
                        SizedBox(height: 5.h),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            if (allowsBiometrics)
              Padding(
                padding: const EdgeInsets.only(right: 16.0),
                child: Align(
                  alignment: Alignment.bottomRight,
                  child: IconButton(
                    icon: const Icon(
                      Icons.fingerprint,
                      size: 48,
                    ),
                    onPressed: () async {
                      await _getAvailableBiometrics();
                    },
                  ),
                ),
              ),
            const Positioned(top: 0, right: 8, child: MyLottie()),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  PreferredSizeWidget _buildAuthenticationAppBar(BuildContext context) {
    return CustomAppBar(
      centerTitle: true,
      title: AppbarTitle(text: "Authentication", textColor: Colors.white),
      styleType: Style.bgFill,
    );
  }

  handleAuth(String pass) async {
    final box = Get.find<GetStorage>();
    final user = box.read(CacheKeys.user);
    final res = await authenticationController.login(
        user["phone_number"].toString(), pass);

    final bool maintainace = box.read(CacheKeys.maintainance);

    if (res) {
      if (maintainace) {
        Get.offAll(() => MaintenancePage());
      } else {
        logger.d("Is from start page: $isFromStartPage");

        if (isFromStartPage) {
          switch (user["status"]) {
            case 0:
              Get.to(() => SignUpPage(
                    isForgotPasswd: true,
                    phoneNumber: user["phone_number"],
                  ));
              break;
            case 1:
              Get.to(() =>   PinPutPage(phoneNumber:user["phone_number"]));
              break;
            case 2:
              Get.to(() => const KYCHomePage());
              break;
            case 3:
              
                                        Get.offAllNamed(
                                            NavRoutes.bottomNavSection);
                                     
              break;
            case 4:
              Get.to(() => const BlockedPage());
              break;
            default:
              Get.to(() => const LoginScreen());
          }
        } else {
          Get.back(result: res);
        }
      }
    } else {
      Snack.show(
        res,
        authenticationController.apiMessage.string,
      );
    }
  }
}
