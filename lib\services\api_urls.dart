/// ignore_for_file: non_constant_identifier_names
library;

class ApiUrls {
  static const String BASE_URL_LOCAL =
      "https://b945-41-209-60-66.ngrok-free.app/";
  static String BASE_URL_DEV = "https://devsalticon.onekitty.co.ke/";
  static String BASE_URL_LIVE = "https://apisalticon.onekitty.co.ke/";

  static const String create_kitty = "kitty/create_kitty/";
  static const String create_payment_kitty = "pay/create_payment/";
  static const String pay_payment_kitty = "pay/pay_kitty/";
  static const String charges = "pay/pay_charges/";
  static const String update_kitty = "kitty/update_kitty/";
  static const String updateEndDate = "kitty/update_end_date/";
  static const String join_group = "kitty/join_whatsapp/";
  static const String contribute_kitty = "kitty/contribute_kitty/";
  static const String get_kitty = "kitty/get_kitty_all/";
  static const String get_kitty_by_id = "kitty/get_kitty/";
  static const String contrProcess = "kitty/process_contribute_kitty/";
  static const String contribsTransactions = "kitty/transactions/";
  static const String filterContribs = "kitty/filter-kitty-transactions/";
  static const String confirmPay = "kitty/check-transaction-status/";
  static const String notifications =
      "kitty/get-notification-accounts/?kitty_id=";
  static const String toggleWhatsapp = "kitty/update-whatsapp-status/";
  static const String getKittyText = "kitty/get-transactions-text/";
  static const String fetchBalance = "kitty/charges/";
  static const String fetchBeneficiaries = 'kitty/beneficiaries/';
  static const String beneficiary = 'kitty/beneficiary/';
  static const String create_kitty_media = "kitty/media/";
  static const String kitty_settings = 'kitty/get-kitty-settings/';
  static const String update_kitty_settings = 'kitty/update-kitty-settings/';
  static const String remove_kitty_whatsapp = 'kitty/notification/';
  static const String deactivateKitty = 'kitty/update-kitty-status/';
  static const String report_kitty = 'kitty/incident/';

  //AUTH
  static const String register = "auth/register/";
  static const String otpConfirm = "auth/confirm_user_otp/";
  static const String setPin = "auth/user_set_pin/";
  static const String forgotPass = "auth/forgot_password/";
  static const String login = "auth/login/";
  static const String getPaymentChannels = "auth/payment-channels/";

  static const String updateKYC = 'user/update-kyc/';

  //Notification
  static const String getNotifications = "user/notifications/";
  static const String getNotificationsTypes = 'user/notification-types/';
  static const String updateReadNotification = 'user/update-notification/';
  static const String deleteNotification = "user/notification/";

  //USER
  static const String getUserStats = "user/user_stats/";
  static const String getUserKitties = "user/user_kitties/";
  static const String getUserTransactions = "kitty/get_kitty_contribs/";
  static const String getUserAllTransactions = "user/user-transactions/";
  static const String searchTransactionQr = "admin/search-verify-transaction/";
  static const String verifyTransactionConfirm =
      "admin/verify-transaction-confirm/";
  static const String topUp = "user/top-up/";
  static const String getUser = "user/get-user-phone/";

  static const String withdrawRequest = "user/withdraw_request/";
  static const String withdrawConfirm = "user/withdraw_confirm/";
  static const String config = "user/get-configs-keys/";
  static const String updateProfile = "user/update-user/";
  static const String getReferKitties = "merchant/kitties/";
  static const String getMerTransac = "merchant/transactions/";
  static const String getCode = "merchant/self-register/";

  //bulk_sms
  static const String sendSmS = "user/send-bulk-sms/";
  static const String confirmSmS = "user/send-bulk-sms/confirm/";
  static const String getMsgs = "user/get-sms/";
  static const String getMsg = "user/get-filter-sms/";
  //SMS GROUPS
  static const String create_msg_group = "notification/sms-group/";
  static const String get_msg_groups = "notification/sms-group/";
  static const String get_sms_by_group_id = "notification/sms-group/";
  static const String delete_sms_by_group_id = "notification/sms-group/";
  static const String add_sms_group_member = "notification/sms-group-member/";

  //Chama
  static const String getChamaMembers = "chama/chama/members/";
  static const String getUserChamas = "chama/chama/user-chama/";
  static const String getAllChamaDetails = "chama/chama/chama-details/";

  static const String getKittyChamaDetails = "chama/chama/kitty/";
  static const String editChamaSettings = "chama/chama/settings/";
  static const String getChamaSettings = "chama/chama/settings/";

  //Penalty
  static const String addPenalty = "chama/penalty/add-chama-penalty/";
  static const String getChamaPenalties = "chama/penalty/chama-penalties/";
  static const String updatePenalty = "chama/penalty/chama-penalty/";
  static const String penalizeMember = "chama/penalty/member-penalty/";
  static const String penalizeMultiple =
      "chama/penalty/member-penalty-multiple/";
  static const String getGeneralPenalties =
      "chama/penalty/get-general-penalties/";
  static const String deletePenalty = "chama/penalty/chama-penalty/";

  //SignatoryTransactions
  static const String transferReq = "chama/chama/transfer/";
  static const String transferConfirm = "chama/chama/transfer/confirm/";
  static const String getSigTra = "chama/chama/signatory-transactions/";
  static const String sigApproval =
      "chama/chama/process-signatory-transaction/";

  //Chama Transctions
  static const String getChamaTransactions = "chama/chama/transactions/";
  static const String postTransactionText = "chama/chama/contribution-message/";

  //signatories
  static const String addSignatory = "chama/chama/signatory/";
  static const String getSignatories = "chama/chama/signatory/";

  //meetings
  static const String addMeeting = "chama/chama/event/";
  static const String getMeeting = "chama/chama/event/";
  static const String updateMeeting = "chama/chama/event/";

  //chama Contributions
  static const String chamaContribute = "chama/chama/pay/";
  static const String chamaContrConfirm = "chama/chama/pay/confirm/";
  static const String checkMemberPenalties = "chama/chama/member-penalties/";

  //chama
  static const String getFreq = "chama/chama/get-enums-configs/";
  static const String create_chama = "chama/chama/create-chama/";
  static const String add_members = "chama/member/add-members/";
  static const String set_order = "chama/chama/set-receiving-order/";
  static const String add_resource = "chama/chama/resource/";
  static const String getResources = "chama/chama/resource/";
  static const String update_chama = "chama/chama/edit-chama/";
  static const String get_all = "chama/chama/chama-details/";
  static const String add_group = "chama/chama/notification/";
  static const String removeM = "chama/member/update-member-status/";
  static const String getbeneficiaries = "chama/chama/chama-details/";
  static const String update_benf_acc =
      "chama/member/update-beneficiary-account/";
  static const String update_resource = "chama/chama/resource/";
  static const String rm_resource = "chama/chama/resource/";
  static const String update_role = "chama/member/update-member-role/";
  static const String update_member = "chama/member/update-member-details/";
  static const String rm_whatsApp = "chama/chama/notification/";
  static const String toggle_whatsApp = "chama/chama/notification/";

  //events
  static const String GETCATEGORIES = "/tickets/admin/get-categories/";
  static const String GETALLEVENTS = "tickets/event/get-events/";
  static const String GETUSEREVENTS = "/tickets/event/get-events-user/";
  static const String CREATEEVENT = "/tickets/event/create-event/";
  static const String ADDSOCIALMEDIA = "/tickets/event/upload-images-social/";
  static const String ADDTICKETS = "/tickets/event/create-tickets/";
  static const String EDITTICKET = "/tickets/event/edit-ticket/";
  static const String GETEVENTBYID = "/tickets/event/get-event-id/";
  static const String GETEVENTBYUSERNAME = "/tickets/event/get-event/";
  static const String EDITEVENT = "/tickets/event/edit-event/";
  static const String UPLOADFILE = "/tickets/media/upload-media/";
  static const String PURCHASETICKETS = "/tickets/event/purchase-tickets/";
  static const String GETEVENTSMEDIA = "/media/media/";
  static const String EVENTTRANSACTIONS = "/tickets/event/transactions/";
  static const String GETDELEGATES = "/kitty/delegates/";
  static const String ADDDELEGATES = "/kitty/delegate/";
  static const String VERIFYTICKET = '/tickets/event/verify-ticket/';
  static const String VERIFYTICKETCONFIRM =
      "tickets/event/verify-ticket/confirm/";
  static const String GETENUMS = '/tickets/event/enums/';
  static const String TRANSFERREQUEST = '/kitty/transfer/';
  static const String TRANSFERCONFIRM = '/kitty/transfer/confirm/';
  static const String SIGNATORYTRANSACTIONS = 'kitty/signatory-transactions/';
  static const String DELETEMEDIA = 'tickets/media/media/';
  static const String PROCESSSIGNATORYTRANSACTIONS =
      'kitty/process-signatory-transaction/';
  static const String RESERVETICKET = 'tickets/event/reserve-ticket/';
  static const String INVITEUSERS =
      'tickets/event/invite-reserve-tickets-users/';
  static const String FETCHINVITEDUSERS =
      'tickets/event/invited-reserve-users/';
}

enum Environmentcurrent { Live, development }

Environmentcurrent? environmentMode;
