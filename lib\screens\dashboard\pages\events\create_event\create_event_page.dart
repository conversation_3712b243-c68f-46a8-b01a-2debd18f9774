// import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/controllers/events/events_controller.dart';
import 'package:onekitty/screens/dashboard/pages/events/create_event/event_completed_page.dart';
import 'package:onekitty/screens/dashboard/pages/events/create_event/event_details.dart';
import 'package:onekitty/screens/dashboard/pages/events/create_event/socials.dart';
import 'package:onekitty/screens/dashboard/pages/events/create_event/tickets.dart';
import 'package:onekitty/screens/dashboard/pages/events/create_event/time_and_location.dart';
import 'package:onekitty/utils/iswysiwyg.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/show_snackbar.dart';
import 'package:onekitty/utils/stepper_indicator.dart';
import '/controllers/events/create_event_controller.dart';
import '/utils/date_formatter.dart';
import 'package:flutter_quill/flutter_quill.dart' as q;
import 'package:onekitty/main.dart' show isLight;

class CreateEventPage extends StatefulWidget {
  const CreateEventPage({super.key});

  @override
  State<CreateEventPage> createState() => CreateEventPageState();
}

class CreateEventPageState extends State<CreateEventPage> {
  PageController pageController = PageController();
  late ValueNotifier<int> currentPage = ValueNotifier(0);
  final q.QuillController eventDescription = q.QuillController.basic();
  final TextEditingController eventTitle = TextEditingController(),
      emailAddress = TextEditingController(),
      phoneNumber = TextEditingController(
          text: Get.find<Eventcontroller>().getLocalUser()?.phoneNumber ?? "");
  final GlobalKey<FormState> _formKey1 = GlobalKey<FormState>();
  // final ValueNotifier<String?> countryCode = ValueNotifier(null);
//controllers for page 2
  final TextEditingController eventStartDate = TextEditingController(),
      eventEndDate = TextEditingController(),
      venue = TextEditingController(),
      referralCode = TextEditingController(text: Get.find<GlobalControllers>().getCode()),
      location = TextEditingController();
  final GlobalKey<FormState> _formKey2 = GlobalKey<FormState>();
  //controllers for page 4
  final TextEditingController website = TextEditingController(),
      facebook = TextEditingController(),
      xlink = TextEditingController(),
      instagram = TextEditingController(),
      tiktok = TextEditingController();
      final controller = Get.find<CreateEventController>();

  @override
  void dispose() {
    eventTitle.dispose();
    eventDescription.dispose();
    emailAddress.dispose();
    phoneNumber.dispose();
    eventStartDate.dispose();
    eventEndDate.dispose();
    venue.dispose();
    location.dispose();
    website.dispose();
    facebook.dispose();
    xlink.dispose();
    instagram.dispose();
    tiktok.dispose();
    pageController.dispose();
    currentPage.dispose(); 
    super.dispose();
  }

  void _handleBack() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Exit Event Creation?'),
        content: const Text('Your progress will be lost.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context); // Close dialog
              Navigator.pop(context); // Exit page
            },
            child: const Text('Exit'),
          ),
        ],
      ),
    );
  }

  void validator(int pageNo) async {
    try {
      if (pageNo == 0) {
        
        if (_formKey1.currentState!.validate()) {
          pageController.animateToPage(currentPage.value + 1,
              duration: const Duration(microseconds: 500),
              curve: Curves.linear);
        }
      } else if (pageNo == 1) {
        if (_formKey2.currentState!.validate()) {
          try {
            final DateTime startDate = DateFormat('dd/MM/yyyy hh:mm a').parse(eventStartDate.text);
            final DateTime endDate = DateFormat('dd/MM/yyyy hh:mm a').parse(eventEndDate.text);
            
            if (endDate.isBefore(startDate)) {
              showSnackbar(context: context, label: 'End date must be after start date');
              return;
            }

            if (endDate.difference(startDate).inMinutes < 30) {
              showSnackbar(context: context, label: 'Event must be at least 30 minutes long');
              return;
            }

            // Validate location data
            final locationController = Get.find<TimeAndLocationController>();
            if (locationController.mapCoordinates['lat'] == null || 
                locationController.mapCoordinates['long'] == null) {
              showSnackbar(context: context, label: 'Please select a location on the map');
              return;
            }

            await controller
                .createEvent(
              eventMedia: controller.eventMedia,
              title: eventTitle.text,
              username:
                  "${eventTitle.text.toLowerCase().replaceAll(RegExp(r'\s+'), '-')}${DateTime.now().millisecondsSinceEpoch}",
              description: quilltoHtml(eventDescription),
              phoneNumber: phoneNumber.text,
              email: emailAddress.text,
              locationTip: location.text,
              venue: venue.text,
              referralCode: referralCode.text.isEmpty ? null : int.tryParse(referralCode.text),
              lat: Get.find<TimeAndLocationController>().mapCoordinates['lat'] ?? 0.0,
              long: Get.find<TimeAndLocationController>().mapCoordinates['long'] ?? 0.0,
              catId: controller.category.value,
              startDate: convertToIso8601(eventStartDate.text).toString(),
              endDate: convertToIso8601(eventEndDate.text).toString(),
            )
                .onError((e, s) {
              showSnackbar(context: context, label: 'Error creating event: $e');
              return 0;
            }).whenComplete(() {
              if (controller.eventId.value == 0) {
                Get.snackbar('Error', 'Couldnt create event');
              } else {
                Get.find<GlobalControllers>().clearCode();
                pageController.animateToPage(currentPage.value + 1,
                    duration: const Duration(microseconds: 500),
                    curve: Curves.linear);
              }
            });

          } catch (e) {
            showSnackbar(context: context, label: 'Invalid date format');
            return;
          }
        }
      } else if (pageNo == 2) {
        if (controller.tickets.isNotEmpty) {
          controller
              .createTickets(
                  eventId: controller.eventId.value,
                  tickets: controller.tickets
                      .map((e) => TicketUpload(
                          ticketQuantity: e.slotLength,
                          ticketGroupSize: e.groupSize,
                          ticketTitle: e.description,
                          ticketPrice: e.price,
                          ticketType: e.ticketType,
                          ticketDescription: "",
                          ticketStartDate:
                              e.purchaseDate.toUtc().toIso8601String(),
                          ticketEndDate:
                              e.purchaseEndDate.toUtc().toIso8601String()))
                      .toList())
              .onError((e, s) {
            showSnackbar(context: context, label: 'Error creating tickets: $e');
            return false;
          }).whenComplete(() {
            if (controller.tickets.isEmpty) {
              showSnackbar(context: context, label: 'Failed to create tickets');
              return;
            }
            pageController.animateToPage(currentPage.value + 1,
                duration: const Duration(microseconds: 500),
                curve: Curves.linear);
          });
        } else {
          showSnackbar(context: context, label: 'Add atleast one ticket');
        }
      } else if (pageNo == 3) {
        try {
          await controller
            .addSocialMedia(
                eventId: controller.eventId.value,
                media: [],
                facebook: facebook.text,
                tiktok: tiktok.text,
                instagram: instagram.text,
                youtube: "",
                twitter: xlink.text,
                hearthis: "",
                website: website.text)
            .whenComplete(() {
          final _eventsController = Get.put(Eventcontroller());
          _eventsController.fetchEvents();
          _eventsController.fetchUserEvents();
          pageController.animateToPage(currentPage.value + 1,
              duration: const Duration(microseconds: 500),
              curve: Curves.linear);
        });
        } catch (e) {
          showSnackbar(context: context, label: 'Error adding social media: $e');
        }
      }
    } catch (e) {
      showSnackbar(context: context, label: 'An error occurred: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    CreateEventController controller = Get.put(CreateEventController());
    return WillPopScope(
      onWillPop: () async {
        _handleBack();
        return false;
      },
      child: Scaffold(
        appBar: AppBar(
          leadingWidth: 100.w,
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          elevation: 4,
          title: Text(
            'Create an Event',
            style: TextStyle(
              color: isLight.value ? Colors.black : Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 20.sp,
            ),
          ),
          leading: TextButton.icon(
            icon:  Icon(Icons.arrow_back, color: isLight.value ? Colors.black : null),
            onPressed: _handleBack,
            label: const Text('Back'),
          ),
        ),
        floatingActionButton: ValueListenableBuilder(
            valueListenable: currentPage,
            builder: (context, _currentPage, child) {
              if (_currentPage == 0) {
                return Material(
                  borderRadius: BorderRadius.circular(25),
                  color: primaryColor,
                  child: MaterialButton(
                      onPressed: () {
                        if (!controller.isUploading.value) {
                          validator(_currentPage);
                        } else {
                          showSnackbar(
                              context: context, label: 'File is uploading');
                        }
                      },
                      child: const Text(
                        'Next',
                        style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 16),
                      )),
                );
              }
              if (_currentPage > 3) {
                return const SizedBox();
              }
              return Padding(
                padding: EdgeInsets.only(left: 18.0.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    _currentPage == 2
                        ? const SizedBox()
                        : TextButton(
                            onPressed: () {
                              pageController.jumpToPage(
                                currentPage.value - 1,
                              );
                            },
                            child: const Text('Back')),
                    Obx(
                      () => Visibility(
                        visible:
                            !(_currentPage == 2 && controller.tickets.isEmpty),
                        child: MyButton(
                          onClick: () async {
                            validator(_currentPage);
                          },
                          label: _currentPage == 3 ? "Finish" : 'Next',
                          showLoading: controller.isloading.value,
                        ),
                      ),
                    )
                  ],
                ),
              );
            }),
        body: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              children: [
                ValueListenableBuilder(
                    valueListenable: currentPage,
                    builder: (context, _currentPage, child) {
                      if (_currentPage > 3) {
                        return const SizedBox();
                      }
                      return Column(
                        children: [
                          StepperIndicator(currentStep: _currentPage),
                        ],
                      );
                    }),
                SizedBox(height: 20.h),
                Expanded(
                  child: PageView(
                    physics: const NeverScrollableScrollPhysics(),
                    reverse: false,
                    controller: pageController,
                    onPageChanged: (page) => currentPage.value = page,
                    children: [
                      EventDetails(
                          formKey: _formKey1,
                          eventTitle: eventTitle,
                          eventDescription: eventDescription,
                          emailAddress: emailAddress,
                          phoneNumber: phoneNumber),
                      TimeAndLocation(
                          referralCode: referralCode,
                          formKey: _formKey2,
                          eventStartDate: eventStartDate,
                          eventEndDate: eventEndDate,
                          venue: venue,
                          location: location),
                      const Tickets(),
                      Socials(
                          website: website,
                          facebook: facebook,
                          xlink: xlink,
                          instagram: instagram,
                          tiktok: tiktok),
                      EventCompletedPage(
                        username: '${controller.eventUsername} ',
                        eventName: '${controller.eventName} ',
                      ),
                    ],
                  ),
                ),
              ],
            ))));
  }
}

class TicketUpload {
  final String ticketTitle;
  final int? ticketQuantity;
  final int ticketPrice;
  final String ticketType;
  final int? ticketGroupSize;
  final String ticketDescription;
  final String ticketStartDate;
  final String ticketEndDate;
  const TicketUpload({
    required this.ticketTitle,
    this.ticketQuantity,
    required this.ticketPrice,
    required this.ticketType,
    this.ticketGroupSize,
    required this.ticketDescription,
    required this.ticketStartDate,
    required this.ticketEndDate,
  });
}

