import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:intl/intl.dart';
import 'package:number_paginator/number_paginator.dart';
import 'package:onekitty/models/auth/user_model.dart';
import 'package:onekitty/models/user_transaction_model.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/export_widget.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/transactions.dart';

import '../../../../../utils/utils_exports.dart';
import 'package:flutter/material.dart';

// ignore_for_file: must_be_immutable
class UserTransactionsScreen extends StatefulWidget {
  const UserTransactionsScreen({super.key});

  @override
  State<UserTransactionsScreen> createState() => _UserTransactionsScreenState();
}

class _UserTransactionsScreenState extends State<UserTransactionsScreen> {
  final UserKittyController controller = Get.find<UserKittyController>();
  TextEditingController searchController = TextEditingController();
  final DataController dataController = Get.find<DataController>();
  UserModelLatest user = UserModelLatest();

  List<String> dropdownItemList = ["code", "Date", "Kitty ID"];
  String selectedFilter = "";
  bool singleTrans = false;
  TextEditingController startDate = TextEditingController();
  TextEditingController endDate = TextEditingController();

  TextEditingController kittController = TextEditingController();
  TextEditingController codeController = TextEditingController();
  List<Item> filterByTitle = [];

  GlobalKey<NavigatorState> navigatorKey = GlobalKey();

  final dateformat = DateFormat('EE, dd MMMM');

  @override
  void initState() {
    super.initState();
    // _loadTransactions();
    filterByTitle = controller.alltransactions;
    searchController.addListener(_onSearchTextChanged);
  }

  void _onSearchTextChanged() {
    setState(() {});
  }

  int? parseInt(String value) {
    try {
      return int.parse(value);
    } catch (e) {
      return null; // Return null if parsing fails
    }
  }

  @override
  Widget build(BuildContext context) {
    controller.getLocalUser();

    return SafeArea(
      child: Scaffold(
        appBar: _buildAppBar(context),
        backgroundColor: appTheme.gray50,
        resizeToAvoidBottomInset: false,
        body: Container(
          width: double.maxFinite,
          padding: EdgeInsets.symmetric(vertical: 16.h),
          child: Column(
            children: [
              // ignore: unrelated_type_equality_checks
              if (selectedFilter == "Kitty ID") _buildKittyFilterUI(context),
              if (selectedFilter == "code") _buildCodeFilterUI(context),
              if (selectedFilter == "Date") _buildDateFilterUI(context),
              if (selectedFilter == "") SizedBox(height: 3.h),
              Expanded(
                child: Container(
                  width: 366.w,
                  height: 869.h,
                  margin: EdgeInsets.symmetric(horizontal: 15.w),
                  child: Align(
                    alignment: Alignment.center,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        startDate.text.isNotEmpty && endDate.text.isNotEmpty ||
                                kittController.text.isNotEmpty ||
                                codeController.text.isNotEmpty
                            ? _buildFilteredTransactionsList(context)
                            : _buildTransactionsList(context)
                      ],
                    ),
                  ),
                ),
              ),
              Container(
                decoration: const BoxDecoration(
                  // color: Theme.of(context).colorScheme.inversePrimary,
                ),
                child: Obx(() {
                  if (controller.alltransactions.isNotEmpty &&
                      controller.results.value.totalPages! > 0) {
                    return Visibility(
                      visible: true,
                      child: NumberPaginator(
                        numberPages: controller.results.value.totalPages ?? 0,
                        onPageChange: (int index) async {
                          await controller.getUserTransactions(
                            phoneNo:
                                controller.getLocalUser()?.phoneNumber ?? "",
                            page: index,
                          );
                          setState(() {});
                        },
                      ),
                    );
                  } else {
                    return Container();
                  }
                }),
              )
            ],
          ),
        ),
        floatingActionButton: Padding(
          padding: const EdgeInsets.only(bottom: 60.0),
          child: CustomElevatedButton(
            buttonStyle: ButtonStyle(
              backgroundColor: WidgetStateProperty.all<Color>(
                  const Color.fromARGB(255, 184, 129, 57)),
            ),
            width: 90.w,
            height: 30.h,
            text: "Export",
            buttonTextStyle:
                TextStyle(fontSize: 12.h, fontWeight: FontWeight.bold),
            leftIcon: Container(
                margin: EdgeInsets.only(right: 1.w),
                child: CustomImageView(
                    imagePath: AssetUrl.expIcon, height: 12.h, width: 12.w)),
            onPressed: () {
              showModalBottomSheet(
                context: context,
                builder: (BuildContext context) {
                  return ExportContentWidget(
                    singleTrans: singleTrans,
                  );
                },
              );
            },
            alignment: Alignment.bottomRight,
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      ),
    );
  }

  // All the pagenated transactions page

  Widget _buildTransactionsList(BuildContext context) {
    return GetX(
      builder: (UserKittyController controller) {
        if (controller.loadingTransactions.isTrue) {
          return SizedBox(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpinKitDualRing(
                    color: ColorUtil.blueColor,
                    lineWidth: 4.sp,
                    size: 40.0.sp,
                  ),
                  const Text(
                    "loading..",
                    style: TextStyle(
                      // color: Colors.white,
                      fontStyle: FontStyle.italic,
                    ),
                  )
                ],
              ),
            ),
          );
        } else if (controller.alltransactions.isEmpty) {
          return const Center(
            child: Text("You have no transactions yet here"),
          );
        } else if (filterByTitle.isEmpty) {
          return Padding(
            padding: EdgeInsets.only(top: 15.h),
            child: const Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text("Transaction not found"),
              ],
            ),
          );
        } else if (filterByTitle.isNotEmpty) {
          return Expanded(
            child: GroupedListView<Item, DateTime>(
              sort: false,
              useStickyGroupSeparators: true,
              elements: filterByTitle,
              groupBy: (Item element) {
                DateTime date = element.createdAt!.toLocal();
                return DateTime(date.year, date.month, date.day);
              },
              groupHeaderBuilder: (value) {
                final date = dateformat.format(value.createdAt!.toLocal());
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    date,
                    style: const TextStyle(
                      // color: Colors.black,
                      fontWeight: FontWeight.bold,
                      fontSize: 20.0,
                    ),
                  ),
                );
              },
              itemBuilder: (_, Item item) {
                return TransactionCard(item: item);
              },
              separator: const Padding(
                padding: EdgeInsets.symmetric(vertical: 8.0),
              ),
            ),
          );
        } else {
          return Container();
        }
      },
    );
  }

// All the Filtered transactions

  Widget _buildFilteredTransactionsList(BuildContext context) {
    return GetX(
      init: UserKittyController(),
      builder: (UserKittyController controller) {
        if (controller.loadingfiltrTransactions.isTrue) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SpinKitDualRing(
                  color: ColorUtil.blueColor,
                  lineWidth: 4.sp,
                  size: 40.0.sp,
                ),
                const Text(
                  "Loading your transactions",
                  style: TextStyle(
                    // color: Colors.white,
                    fontStyle: FontStyle.italic,
                  ),
                )
              ],
            ),
          );
        } else if (controller.filtrtransactions.isEmpty) {
          return const Center(child: Text("could not find the transactions"));
        } else if (controller.filtrtransactions.isNotEmpty) {
          return Expanded(
            child: GroupedListView<Item, DateTime>(
              sort: false,
              elements: controller.filtrtransactions,
              groupBy: (Item element) {
                DateTime date = element.createdAt!.toLocal();
                return DateTime(date.year, date.month, date.day);
              },
              groupHeaderBuilder: (value) {
                final date = dateformat.format(value.createdAt!.toLocal());
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    date,
                    style: const TextStyle(
                      // color: Colors.black,
                      fontWeight: FontWeight.bold,
                      fontSize: 20.0,
                    ),
                  ),
                );
              },
              itemBuilder: (_, Item item) {
                return TransactionCard(item: item);
              },
              separator: const Padding(
                padding: EdgeInsets.symmetric(vertical: 8.0),
              ),
            ),
          );
        } else {
          return Expanded(
            child: GroupedListView<Item, DateTime>(
              sort: false,
              elements: controller.filtrtransactions,
              groupBy: (Item element) {
                DateTime date = element.createdAt!;
                return DateTime(date.year, date.month, date.day);
              },
              groupHeaderBuilder: (value) {
                final date = dateformat.format(value.createdAt!.toLocal());
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Text(
                    date,
                    style: const TextStyle(
                      // color: Colors.black,
                      fontWeight: FontWeight.bold,
                      fontSize: 12.0,
                    ),
                  ),
                );
              },
              itemBuilder: (_, Item item) {
                return TransactionCard(item: item);
              },
              separator: const Padding(
                padding: EdgeInsets.symmetric(vertical: 8.0),
              ),
            ),
          );
        }
      },
    );
  }

  ///Custom App Bar
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return PreferredSize(
      preferredSize: Size.fromHeight(60.h),
      child: Padding(
        padding: EdgeInsets.only(top: 20.h),
        child: AppBar(
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.black87,
            ),
            onPressed: () {
              // Handle back button press here
              Navigator.of(context).pop();
            },
          ),
          toolbarHeight: 160.h,
          // backgroundColor: Colors.white,
          title: _buildRow(context),
        ),
      ),
    );
  }

  // Phone filter UI

  Widget _buildKittyFilterUI(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomSearchView(
            textInputType: TextInputType.number,
            width: 170.w,
            controller: kittController,
            hintText: "Filter by kitty",
            contentPadding: EdgeInsets.all(5.h),
            onChanged: (p0) {
              if (p0.length == 4) {
                _updateFilter();
              }
            },
          ),
          InkWell(
            onTap: () {
              setState(() {
                selectedFilter = "Filter";
                kittController.clear();
              });
            },
            child: CustomImageView(
              imagePath: AssetUrl.imgIconoirCancel,
              height: 24.h,
              width: 24.w,
            ),
          ),
        ],
      ),
    );
  }

  ////Transaction filter UI

  Widget _buildCodeFilterUI(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomSearchView(
            width: 170.w,
            controller: codeController,
            hintText: "Filter with code",
            contentPadding: EdgeInsets.all(5.h),
            onChanged: (p0) {
              _updateFilter();
            },
          ),
          InkWell(
            onTap: () {
              setState(() {
                selectedFilter = "Filter";
                codeController.clear();
              });
            },
            child: CustomImageView(
              imagePath: AssetUrl.imgIconoirCancel,
              height: 24.h,
              width: 24.w,
            ),
          ),
        ],
      ),
    );
  }

  ///Date Filtering

  Widget _buildDateFilterUI(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 12.0, right: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 120.w,
            height: 35.h,
            child: TextFormField(
              controller: startDate,
              style: const TextStyle(fontSize: 15),
              readOnly: false,
              onTap: () async {
                final DateTime? pickedDate = await showDatePicker(
                  context: context,
                  initialDate:
                      DateTime.now(), // Set initial date to current date
                  firstDate: DateTime(2000),
                  lastDate:
                      DateTime(2100), // Set the latest date that can be picked
                );

                if (pickedDate != null) {
                  final formattedDate =
                      DateFormat('yyyy-MM-dd').format(pickedDate);
                  startDate.text = formattedDate;
                  // _updateDateFilter();
                }
              },
              decoration: InputDecoration(
                labelText: 'Start Date',
                border: const OutlineInputBorder(
                  // borderSide: BorderSide(color: Colors.black87),
                ),
                focusedBorder: const OutlineInputBorder(
                    // borderSide: BorderSide(color: Colors.black87)
                    ),
                suffixIcon: Icon(
                  Icons.calendar_today,
                  size: 18.h,
                ),
              ),
            ),
          ),
          SizedBox(
            width: 8.w,
          ),
          SizedBox(
            width: 120.w,
            height: 35.h,
            child: TextFormField(
              onEditingComplete: () {
                // _loadFiltrTransactions(startDate.text, endDate.text);
              },
              controller: endDate,
              style: const TextStyle(fontSize: 15),
              readOnly: false,
              onTap: () async {
                final DateTime? pickedDate = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                );

                if (pickedDate != null) {
                  final formattedDate =
                      DateFormat('yyyy-MM-dd').format(pickedDate);
                  endDate.text = formattedDate;
                  _updateFilter();
                }
              },
              decoration: InputDecoration(
                labelText: 'End Date',
                border: const OutlineInputBorder(
                  // borderSide: BorderSide(color: Colors.black87),
                ),
                focusedBorder: const OutlineInputBorder(
                    // borderSide: BorderSide(color: Colors.black87)
                    ),
                suffixIcon: Icon(
                  Icons.calendar_today,
                  size: 18.h,
                ),
              ),
            ),
          ),
          SizedBox(
            width: 8.w,
          ),
          InkWell(
            onTap: () {
              setState(() {
                selectedFilter = "Filter";

                startDate.clear();
                endDate.clear();
              });
            },
            child: CustomImageView(
              imagePath: AssetUrl.imgIconoirCancel,
              height: 24.h,
              width: 24.w,
            ),
          ),
        ],
      ),
    );
  }

  /// Section Widget
  Widget _buildRow(BuildContext context) {
    return Row(
      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: TextFormField(
            onChanged: (value) {
              filterByTitle = controller.alltransactions
                  .where((p0) => p0.kittyTitle!
                      .toLowerCase()
                      .contains(value.toLowerCase()))
                  .toList();
              setState(() {});
            },
            controller: searchController,
            decoration: const InputDecoration(
              focusedBorder: OutlineInputBorder(borderSide: BorderSide.none),
              enabledBorder: OutlineInputBorder(borderSide: BorderSide.none),
              border: OutlineInputBorder(
                borderSide: BorderSide.none,
              ),
              hintText: "Search",
              suffixIcon: Icon(Icons.search),
            ),
          ),
        ),
        const SizedBox(
          width: 5,
        ),
        CustomDropDown(
            fillColor: Colors.blue,
            width: 110.w,
            hintText: "Filter",
            // hintStyle: TextStyle(fontSize: 12.h, color: appTheme.indigo500),
            items: dropdownItemList,
            prefix: Container(
              margin: EdgeInsets.fromLTRB(4.w, 11.h, 0.w, 11.h),
              child: CustomImageView(
                  imagePath: AssetUrl.imgIconIndigo500,
                  height: 40.h,
                  width: 18.w),
            ),
            prefixConstraints: BoxConstraints(maxHeight: 40.h, maxWidth: 20.h),
            onChanged: (value) {
              setState(() {
                selectedFilter = value;
              });
            }),
      ],
    );
  }

  void _updateFilter() {
    setState(() {
      _fetchFilteredTransactions();
    });
  }

  void _fetchFilteredTransactions() async {
    try {
      await Get.find<UserKittyController>().getUserFiltrContributions(
        phoneNo: controller.getLocalUser()?.phoneNumber ?? "",
        kittId: parseInt(kittController.text),
        startDate: startDate.text,
        endDate: endDate.text,
        code: codeController.text,
      );

      setState(() {
        Get.find<UserKittyController>().loadingfiltrTransactions(false);
      });
    } catch (e) {
      throw e;
    }
  }
}
