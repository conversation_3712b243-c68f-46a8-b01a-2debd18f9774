import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/kitty/media_model.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/models/user_kitties_model.dart';
import 'package:onekitty/models/auth/user_model.dart';
import 'package:onekitty/models/user_transaction_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';

import '../utils/cache_keys.dart';

class UserKittyController extends GetxController implements GetxService {
  final HttpService apiProvider = Get.put(
    HttpService(),
  );
//getting user kitties
  RxBool kittiesLoading = false.obs;
  Rx<UserModelLatest> user = UserModelLatest().obs;

  final box = Get.find<GetStorage>();
  RxString apiMessage = ''.obs;
  final logger = Get.find<Logger>();
  RxBool status = false.obs;

  Rx<UserModelLatest> usermodel = UserModelLatest().obs;
  Rx<MerchantModel> usermerchant = MerchantModel().obs;

  RxList<UserKitty> kitties = <UserKitty>[].obs;
  RxList<Kitty> kittyList = <Kitty>[].obs;
  RxList<MediaModel> media = <MediaModel>[].obs;

//
  RxBool topUploading = false.obs;
  RxMap topUpData = {}.obs;
  RxString apiMessageTopup = ''.obs;

//get user_kitty transactions
  RxBool loadingTransactions = false.obs;
  RxList<Item> alltransactions = <Item>[].obs;
  Rx<UserTransactionModel> results = UserTransactionModel().obs;
  RxBool loadingfiltrTransactions = false.obs;
  RxList<Item> filtrtransactions = <Item>[].obs;
  RxBool isloadingUser = false.obs;

  //referKitties
  RxList<Kitty> referkitties = <Kitty>[].obs;
  RxInt totalRefers = 0.obs;
  RxString kittyType = ''.obs;
  Rx<transData> resultsDts = transData().obs;
  RxList<transItem> merchtransactions = <transItem>[].obs;

  RxBool loading = false.obs;

  // --- Pagination and Infinite Scroll ---
  final scrollController = ScrollController();
  RxBool loadingMore = false.obs; // Indicates loading more items
  RxInt currentPage = 0.obs;
  RxInt pageSize = 10.obs; // Default page size, can be adjusted
  RxInt totalKitties = 0.obs;
  RxInt totalPages = 0.obs;
  RxBool isLastPage = false.obs; // Flag from API if it's the last page
  // --- Search functionality ---
  RxBool searchLoading = false.obs; // Track search loading state
  RxString searchQuery = ''.obs; // Current search query
  // --- End Search ---
  
  // --- Caching and optimization improvements ---
  final Map<int, List<UserKitty>> _pageCache = {}; // Cache pages by page number
  RxBool isPrefetching = false.obs; // Track prefetching state
  RxDouble savedScrollOffset = 0.0.obs; // Save scroll position for restoration
  RxBool useCache = true.obs; // Toggle for cache usage (useful for forced refresh)
  // --- End caching improvements ---
  
  // --- End Pagination ---

//get user_all transctions

  void _scrollListener() async {
    if (!scrollController.hasClients) return;
    
    // Save scroll position for restoration
    savedScrollOffset.value = scrollController.position.pixels;
    
    // Check if we're within 20% of the bottom and not already loading or on the last page
    if (scrollController.position.pixels >= scrollController.position.maxScrollExtent * 0.8 && 
        !loadingMore.value && 
        !isLastPage.value) {
      logger.i("Scroll listener triggered: Reached end, loading more kitties...");
      await loadMoreKitties();
    }
    
    // Prefetch next page when user scrolls to 60% of the list
    // This gives us a head start before the user reaches the 80% threshold for loadMore
    if (scrollController.position.pixels >= scrollController.position.maxScrollExtent * 0.6 &&
        !isPrefetching.value && 
        !isLastPage.value && 
        !loadingMore.value) {
      _prefetchNextPage();
    }
  }

  @override
  void onInit() {
    super.onInit();
    getLocalUser(); // Load user data first
    scrollController.addListener(_scrollListener); // Add listener for infinite scroll
    
    // Restore scroll position if returning to the screen
    // We use a slight delay to ensure the list has been built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (savedScrollOffset.value > 0 && scrollController.hasClients) {
        scrollController.jumpTo(savedScrollOffset.value);
      }
    });
  }

  void reset() {
    // Reset pagination state for refresh
    currentPage.value = 0;
    isLastPage.value = false;
    totalKitties.value = 0;
    totalPages.value = 0;
    kitties.clear(); // Clear existing kitties on reset/refresh
    _pageCache.clear(); // Clear cache on reset
    useCache.value = false; // Disable cache briefly for fresh data
    savedScrollOffset.value = 0.0; // Reset saved scroll position
  }

  UserModelLatest? getLocalUser() {
    final usr = box.read(CacheKeys.user);
    if (usr != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        user(UserModelLatest.fromJson(usr));
      });
      return user.value;
    } else {
      return null;
    }
  }

  UserKitty? findKittyById(int id) {
    return kitties.firstWhere(
      (kitty) => kitty.kitty?.id == id,
    );
  }

  getUser() async {
    isloadingUser(true);
    try {
      var resp = await apiProvider.request(
          url: "${ApiUrls.getUser}?phone_number=${user.value.phoneNumber}",
          method: Method.GET);

      if (resp.data["status"]) {
        final u = resp.data["data"]["user"];
        usermodel(UserModelLatest.fromJson(u));
        // balance=resp.data["data"]["user"]["balance"];
        logger.log(Level.debug, resp.data);
        if (resp.data["data"]["merchant"] != null) {
          final m = resp.data["data"]["merchant"];
          usermerchant(MerchantModel.fromJson(m));
        }
        box.write(CacheKeys.user, u);
      }
      isloadingUser(false);
      update();
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured');
      update();
      isloadingUser(false);
      throw e;
    }
  }

  // Updated to handle pagination info from API with cache support
  getUserkitties({int? page, int? size}) async {
    // Use provided page or current page
    final effectivePage = page ?? currentPage.value;
    // Use controller's pageSize if size is not provided
    final effectiveSize = size ?? pageSize.value;

    // Check if we have this page cached and can use cache
    if (useCache.value && 
        _pageCache.containsKey(effectivePage) && 
        effectivePage > 0 && 
        searchQuery.isEmpty) {
      logger.i("✓ Serving page $effectivePage from cache");
      
      // For subsequent pages, add from cache
      if (effectivePage > 0) {
        kitties.addAll(_pageCache[effectivePage]!);
        // Update state
        currentPage.value = effectivePage;
        if (effectivePage >= totalPages.value - 1 || kitties.length >= totalKitties.value) {
          isLastPage.value = true;
        }
        update();
        return;
      }
    }
    
    // If not cached or first page, proceed with API call
    
    // Show main loading indicator only for the first page fetch
    if (effectivePage == 0) {
      kittiesLoading(true);
    } else {
      // Indicate loading more items for subsequent pages
      loadingMore(true);
    }
    update(); // Notify listeners
    
    // Re-enable cache for future calls
    useCache.value = true;

    try {
      logger.i("Fetching kitties: page=$effectivePage, size=$effectiveSize");
      
      var resp = await apiProvider.request(
          url:
              "${ApiUrls.getUserKitties}?phone_number=${user.value.phoneNumber}&page=$effectivePage&size=$effectiveSize",
          method: Method.GET);

      status(resp.data["status"]);
      apiMessage(resp.data["message"]);

      if (resp.data["status"]) {
        final data = resp.data["data"];
        
        // Clear kitties list only if this is the first page (page 0)
        if (effectivePage == 0) {
          kitties.clear();
          _pageCache.clear(); // Clear cache when fetching first page
        }

        // Update pagination state from API response
        totalKitties(data["total"] ?? data["kitties_count"] ?? 0);
        totalPages(data["total_pages"] ?? 1);
        
        // Important: Update currentPage based on the fetched page
        currentPage(effectivePage);
        
        // Check if this is the last page
        isLastPage(data["last"] ?? (effectivePage >= (data["total_pages"] ?? 1) - 1) || 
                  kitties.length >= (data["total"] ?? data["kitties_count"] ?? 0));

        logger.i("API Response Pagination: page=$effectivePage, totalPages=${totalPages.value}, totalKitties=${totalKitties.value}, isLast=${isLastPage.value}");

        // Add new kitties from the response - Fix the type conversion issue
        final newKittiesData = (data["user_kitties"] ?? []) as List<dynamic>;
        
        if (newKittiesData.isNotEmpty) {
          // Properly convert each item to UserKitty
          final newKittiesList = newKittiesData
              .map((element) => UserKitty.fromJson(element))
              .toList();
          
          // Cache this page for future reference
          _pageCache[effectivePage] = newKittiesList;
          
          // Instead of just adding to the list which can cause scroll jumps,
          // we'll carefully update our list
          if (effectivePage > 0) {
            // For subsequent pages, add to existing list
            kitties.addAll(newKittiesList);
          } else {
            // For first page, replace list
            kitties.value = RxList<UserKitty>.from(newKittiesList);
          }
          logger.i("Added ${newKittiesList.length} kitties, total now: ${kitties.length}/${totalKitties.value}");
          
          // After successfully loading a page, prefetch the next one if not last page
          if (!isLastPage.value && effectivePage == 0) {
            _prefetchNextPage();
          }
        } else {
          logger.w("No kitties returned for page $effectivePage");
          // If no kitties returned on a page beyond 0, we might be at the end
          if (effectivePage > 0) {
            isLastPage(true);
          }
        }

        if (data["media"] != null) {
          // Handle media if necessary, potentially clearing only if page == 0
          if (effectivePage == 0) media.clear();
          for (var element in data["media"]) {
            media.add(MediaModel.fromJson(element));
          }
        }
      } else {
         logger.w("API call failed or status false: ${resp.data["message"]}");
      }
    } catch (e) {
      logger.e("Error fetching kitties: $e");
      apiMessage('An error occurred while fetching kitties');
    } finally {
      // Ensure loading indicators are turned off
      if (effectivePage == 0) {
        kittiesLoading(false);
      } else {
        loadingMore(false);
      }
      update(); // Notify listeners of final state
    }
  }

  Future<void> loadMoreKitties() async {
    // Prevent concurrent calls or loading beyond the last page
    if (loadingMore.value || isLastPage.value) {
      logger.i("Skipping loadMoreKitties: loadingMore=${loadingMore.value}, isLastPage=${isLastPage.value}");
      return;
    }

    // Calculate next page and fetch
    final nextPage = currentPage.value + 1;
    logger.i("Loading more kitties: page=$nextPage (current=${currentPage.value})");
    await getUserkitties(page: nextPage);
  }

  // Search kitties by title via API
  Future<bool> searchKitties(String query) async {
    if (query.isEmpty) {
      // If query is empty, reset to normal browsing
      searchQuery.value = '';
      reset();
      return getUserkitties(page: 0).then((_) => true);
    }
    
    searchQuery.value = query;
    searchLoading(true);
    // Reset pagination state for new search
    kitties.clear();
    currentPage.value = 0;
    isLastPage.value = false;
    // Don't use cache for search results
    useCache.value = false;
    
    try {
      logger.i("Searching kitties with query: '$query'");
      
      var resp = await apiProvider.request(
          url: "${ApiUrls.getUserKitties}?phone_number=${user.value.phoneNumber}&search=$query&page=0&size=${pageSize.value}",
          method: Method.GET);

      status(resp.data["status"]);
      apiMessage(resp.data["message"]);

      if (resp.data["status"]) {
        final data = resp.data["data"];
        
        // Update pagination state from search response
        totalKitties(data["total"] ?? data["kitties_count"] ?? 0);
        totalPages(data["total_pages"] ?? 1);
        isLastPage(data["last"] ?? true); // Assuming search usually returns fewer results
        
        // Process search results - Fix type conversion issue
        final searchResultsData = (data["user_kitties"] ?? []) as List<dynamic>;
        kitties.clear(); // Ensure list is clear before adding
        
        if (searchResultsData.isNotEmpty) {
          // Properly convert each item to UserKitty
          final searchResults = searchResultsData
              .map((element) => UserKitty.fromJson(element))
              .toList();
              
          kitties.value = RxList<UserKitty>.from(searchResults);
          logger.i("Search found ${kitties.length} kitties for query '$query'");
        }
        
        // Handle media if needed
        if (data["media"] != null) {
          media.clear();
          for (var element in data["media"]) {
            media.add(MediaModel.fromJson(element));
          }
        }
        
        return true;
      } else {
        logger.w("Search API call failed: ${resp.data["message"]}");
        return false;
      }
    } catch (e) {
      logger.e("Error searching kitties: $e");
      apiMessage('An error occurred while searching kitties');
      return false;
    } finally {
      searchLoading(false);
      update();
    }
  }

  getReferkitties({int? page = 0, int? size = 10}) async {
    kittiesLoading(true);
    update();

    try {
      var resp = await apiProvider.request(
          url:
              "${ApiUrls.getReferKitties}?code=${usermerchant.value.merchantCode}&page=$page&size=$size",
          method: Method.GET);
      status(resp.data["status"]);
      apiMessage(resp.data["message"]);
      if (resp.data["status"]) {
        referkitties([]);
        for (var element in resp.data["data"]["items"] ?? []) {
          referkitties.add(Kitty.fromJson(element));
        }
        if (referkitties.isNotEmpty) {
          totalRefers.value = resp.data["data"]["total"];
        }
      }
      kittiesLoading(false);
      update();
    } catch (e) {
      kittiesLoading(false);
      logger.e(e);
      apiMessage('An error occured');
      update();

      throw e;
    }
  }

  getUserTransactions(
      {required String phoneNo,
      int? page = 0,
      int? size = 20,
      int? kittId}) async {
    loadingTransactions(true);
    update();
    // try {
      var resp = await apiProvider.request(
        url:
            "${ApiUrls.getUserAllTransactions}?phone_number=$phoneNo&page=$page&size=$size",
        method: Method.GET,
      );
      if (resp.statusCode == 200) {
        UserTransactionModel Results = UserTransactionModel.fromJson(resp.data);

        alltransactions([]);
        for (var items in resp.data["items"] ?? []) {
          alltransactions.add(Item.fromJson(items));
        }

        Results.items = alltransactions;

        results.value = Results;
      } else {
        alltransactions([]);
      }
      loadingTransactions(false);
      update();
    // } catch (e) {
    //   logger.e(e);
    //   apiMessage('An error occured');
    //   loadingTransactions(false);
    //   update();
    // }
  }

  getUserFiltrContributions({
    required String phoneNo,
    int? page = 0,
    int? size = 100,
    String? startDate,
    String? endDate,
    String? code,
    int? kittId,
  }) async {
    loadingfiltrTransactions(true);
    update();
    try {
      String url = "${ApiUrls.getUserAllTransactions}?phone_number=$phoneNo";

      if (!(startDate?.isEmpty ?? true) && !(endDate?.isEmpty ?? true)) {
        url += "&start-date=$startDate&end-date=$endDate";
      } else if (kittId != null) {
        url += "&kitty_id=$kittId";
      } else if (!(code?.isEmpty ?? true)) {
        url += "&transaction_code=$code";
      }

      var resp = await apiProvider.request(
        url: url,
        method: Method.GET,
      );

      if (resp.statusCode == 200 && resp.data["items"] != null) {
        filtrtransactions.clear();
        for (var element in resp.data["items"]) {
          filtrtransactions.add(Item.fromJson(element));
        }
        update();
      }
    } catch (e) {
      logger.e(e);
      apiMessage('An error occurred while fetching filtered transactions');
    } finally {
      loadingfiltrTransactions(false);
      update();
    }
  }

  getMerchantTransactions(
      {required int code, int? page = 0, int size = 100, int? kittId}) async {
    loadingTransactions(true);
    update();
    // try {
    var resp = await apiProvider.request(
      url: kittId != null
          ? "${ApiUrls.getMerTransac}?code=$code&page=$page&size=$size&kitty_id=$kittId"
          : "${ApiUrls.getMerTransac}?code=$code&page=$page&size=$size",
      method: Method.GET,
    );
    if (resp.statusCode == 200) {
      Transac Results = Transac.fromJson(resp.data);

      merchtransactions([]);
      for (var items in resp.data["data"]["items"] ?? []) {
        merchtransactions.add(transItem.fromJson(items));
      }
      Results.data = resultsDts.value;
    } else {
      merchtransactions([]);
    }
    loadingTransactions(false);
    update();
    // } catch (e) {
    //   logger.e(e);
    //   apiMessage('An error occured');
    //   loadingTransactions(false);
    //   update();
    // }
  }

  Future<bool> setRefererCode({required SetMrchtDto request}) async {
    loading(true);
    update();

    try {
      var res = await apiProvider.request(
          url: ApiUrls.getCode, method: Method.POST, params: request.toJson());
      apiMessage(res.data["message"]);
      if (res.data["status"]) {
        apiMessage(res.data["message"]);
      } else {
        Get.snackbar(
          "error",
          res.data["message"],
          backgroundColor: Colors.red,
        );
      }
      loading(false);
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      Get.snackbar(
        "error",
        "$e",
        backgroundColor: Colors.amber,
      );
      loading(false);
      apiMessage("An error occured");
      return false;
    }
  }

  String maskString(String input) {
    if (input.length > 9) {
      final int startLength = (input.length - 5) ~/ 2;
      final int endLength = input.length - startLength - 5;
      final String start = input.substring(0, startLength);
      final String end = input.substring(input.length - endLength);
      final String masked = '*' * 5;
      return '$start$masked$end';
    } else {
      return input;
    }
  }

  Future<bool> topup({
    required String phoneNumber,
    required int amount,
    required int channel,
    required int userId,
    String? email,
  }) async {
    topUploading(true);
    try {
      var res = await apiProvider.request(
        url: ApiUrls.topUp,
        method: Method.POST,
        params: {
          "amount": amount,
          "phone_number": phoneNumber,
          "channel_code": channel,
          "user_id": userId,
          "payer_email": email,
        },
      );
      topUploading(false);
      if (res.data["status"]) {
        topUpData(res.data["data"]);
        apiMessageTopup(res.data["message"]);

        return true;
      } else {
        apiMessageTopup(res.data["message"]);

        return false;
      }
    } catch (e) {
      logger.e(e);
      topUploading(false);
      apiMessageTopup('An error occured');

      return false;
    }
  }

  // Prefetch the next page in the background
  Future<void> _prefetchNextPage() async {
    if (isLastPage.value || isPrefetching.value) return;

    final nextPage = currentPage.value + 1;
    
    // Check if nextPage is already cached
    if (_pageCache.containsKey(nextPage)) {
      logger.i("Page $nextPage already cached, skipping prefetch");
      return;
    }
    
    isPrefetching(true);
    logger.i("Prefetching page $nextPage in background");
    
    try {
      var resp = await apiProvider.request(
        url: "${ApiUrls.getUserKitties}?phone_number=${user.value.phoneNumber}&page=$nextPage&size=${pageSize.value}",
        method: Method.GET
      );
      
      if (resp.data["status"]) {
        final data = resp.data["data"];
        final newKittiesData = (data["user_kitties"] ?? []) as List<dynamic>;
        
        if (newKittiesData.isNotEmpty) {
          // Convert and cache the kitties for future use
          final newKittiesList = newKittiesData
              .map((element) => UserKitty.fromJson(element))
              .toList();
              
          // Store in cache for later use
          _pageCache[nextPage] = newKittiesList;
          logger.i("✓ Successfully prefetched and cached page $nextPage (${newKittiesList.length} items)");
        }
      }
    } catch (e) {
      logger.e("Error prefetching page $nextPage: $e");
      // Prefetch errors are non-critical, we don't need to show them to the user
    } finally {
      isPrefetching(false);
    }
  }

  // Force refresh data from API
  Future<void> forceRefresh() async {
    // Clear cache and disable it temporarily
    _pageCache.clear();
    useCache.value = false;
    // Reset pagination state
    reset();
    // Fetch fresh data
    await getUserkitties(page: 0);
  }

  @override
  void onClose() {
    scrollController.removeListener(_scrollListener); // Clean up listener
    scrollController.dispose(); // Dispose controller
    super.onClose();
  }
}

class DataController extends GetxController {
  Rx<UserKitty> kitty = UserKitty().obs;
}
