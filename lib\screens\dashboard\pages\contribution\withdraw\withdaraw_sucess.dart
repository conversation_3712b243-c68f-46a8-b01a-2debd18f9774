import 'package:confetti/confetti.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/viewing_single_kitty/widgets/kitty_transactions_widget.dart';
import 'package:onekitty/screens/dashboard/pages/events/signatory_transactions.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/confetti/confentti_widget.dart';
import 'package:onekitty/utils/formatted_currency.dart';

// ignore: must_be_immutable
class WithdrawSuccess extends StatefulWidget {
  const WithdrawSuccess({super.key});

  @override
  State<WithdrawSuccess> createState() => _WithdrawSuccessState();
}

class _WithdrawSuccessState extends State<WithdrawSuccess> {
  final ContributeController contributeController = Get.find();
  final DataController dataController = Get.put(DataController());

  KittyController controller = Get.put(KittyController());
  late ConfettiController _controllerCenter;
  final box = GetStorage();

  @override
  void initState() {
    super.initState();
    _controllerCenter =
        ConfettiController(duration: const Duration(seconds: 8));
    _controllerCenter.play();
  }

  @override
  void dispose() {
    _controllerCenter.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        margin: const EdgeInsets.symmetric(horizontal: 30, vertical: 20),
        child: Column(
          children: [
            Row(
              children: [
                IconButton(
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    icon: const Icon(Icons.arrow_back)),
                const Text("Back")
              ],
            ),
            const SizedBox(
              height: 20,
            ),
            Text(
              dataController.kitty.value.kitty?.title ?? "",
              textAlign: TextAlign.center,
              style: context.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(
              height: 30,
            ),
            ConfettiCustom(controllerCenter: _controllerCenter),
            SvgPicture.asset(AssetUrl.tickCircle),
            const SizedBox(
              height: 30,
            ),
            Text(
              "Withdrawal successful",
              style: context.titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 20),
            ),
            const SizedBox(
              height: 15,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: Obx(() => Text(
                controller.withdrawData["has_signatory_transaction"] == true
                    ? "Your withdrawal request has been submitted successfully. The transaction requires verification by signatories before the transfer can be completed."
                    : "Funds shall be transferred to the specified beneficiary channel",
                textAlign: TextAlign.center,
                style: context.titleSmall?.copyWith(
                    color: AppColors.greyTextColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 15),
              )),
            ),
            const SizedBox(
              height: 10,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(AssetUrl.money),
                Text(
                  "Amount withdrawn ",
                  style: context.titleSmall?.copyWith(
                      color: AppColors.greyTextColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 15),
                ),
                Text(
                    controller.withdrawData["amount"] ??
                        controller.withdrawData["response_description"] ??
                        FormattedCurrency()
                            .getFormattedCurrency(box.read("amountbox")),
                    style: context.dividerTextSmall?.copyWith(fontSize: 15)),
              ],
            ),
            const SizedBox(
              height: 30,
            ),
            Obx(() => controller.withdrawData["has_signatory_transaction"] == true
                ? Column(
                    children: [
                      OutlinedButton(
                          onPressed: () {
                            
                            Get.to(()=> SignatoryTransactions(
                                      kittyId: contributeController.kittGoten.value.id??0));
                          },
                          child: const Padding(
                            padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 12),
                            child: Text(
                              "View Signatories",
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                          )),
                      const SizedBox(height: 10),
                      OutlinedButton(
                          onPressed: () {
                            Get.to((()=>const TransactionWidget()));
                          },
                          child: const Padding(
                            padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 12),
                            child: Text(
                              "View Transaction Details",
                              style: TextStyle(fontWeight: FontWeight.bold),
                            ),
                          )),
                    ],
                  )
                : OutlinedButton(
                    onPressed: () {
                      Get.to((()=>const TransactionWidget()));
                    },
                    child: const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 12),
                      child: Text(
                        "View Transaction Details",
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    )))
          ],
        ),
      ),
    );
  }
}
