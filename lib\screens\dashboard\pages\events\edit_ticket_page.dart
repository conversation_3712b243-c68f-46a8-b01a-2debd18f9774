import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/main.dart' show isLight;
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/controllers/events/edit_ticket_controller.dart';
import 'package:onekitty/utils/date_formatter.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:onekitty/utils/my_text_field.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/timeSince.dart';
import 'package:shimmer/shimmer.dart';

class EditTickectPage extends StatefulWidget {
  // final List<Ticket>? ticket;
  final int eventId;
  const EditTickectPage({super.key, required this.eventId});

  @override
  State<EditTickectPage> createState() => _EditTickectPageState();
}

class _EditTickectPageState extends State<EditTickectPage> {
  final ValueNotifier<int?> _selectedIndex = ValueNotifier(null);
  final controller = Get.put(EditTicketController());

  @override
  void dispose() {
    _selectedIndex.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    controller.fetchTickets(widget.eventId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        floatingActionButton: FloatingActionButton(
            backgroundColor: primaryColor,
            onPressed: () async {
              Get.dialog(AddTicketDialog(eventId: widget.eventId));
            },
            child: const Icon(Icons.add, color: Colors.white)),
        appBar: AppBar(
          centerTitle: true,
          title: const Text('Tickets'),
          backgroundColor: primaryColor,
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            await controller.fetchTickets(widget.eventId);
          },
          child: SizedBox(
            child: GetBuilder(
                init: EditTicketController(),
                initState: (_) async {
                  await controller.fetchTickets(widget.eventId);
                },
                builder: (controller) {
                  if (controller.isFetchingTickets.value) {
                    ListView.builder(
                        itemCount: 3,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          return Shimmer.fromColors(
                            baseColor: isLight.value
                                ? Colors.grey[300]!
                                : Colors.grey[900]!,
                            highlightColor: isLight.value
                                ? Colors.grey[100]!
                                : Colors.grey[700]!,
                            child: Container(
                              margin: const EdgeInsets.all(4),
                              width: double.infinity,
                              height: 100.h,
                              color: isLight.value
                                  ? Colors.white
                                  : Colors.grey[900],
                            ),
                          );
                        });
                  } else if (controller.tickets?.value == null ||
                      controller.tickets!.value!.isEmpty) {
                    return Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Text('No Tickets Found.'),
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              TextButton(
                                  onPressed: () {
                                    showDialog(
                                        context: context,
                                        builder: (BuildContext context) {
                                          return AddTicketDialog(
                                              eventId: widget.eventId);
                                        });
                                  },
                                  child: const Text('Click Here')),
                              const Text('To add one'),
                            ],
                          )
                        ],
                      ),
                    );
                  } else {
                    return ListView.builder(
                        itemCount: controller.tickets!.value!.length,
                        itemBuilder: (context, index) {
                          final ticket = controller.tickets!.value![index];
                          // if (ticket.status == Status.DELETED) {
                          //   return SizedBox();
                          // }
                          final group = ticket.ticketType == "GROUP"
                              ? true.obs
                              : false.obs;
                          final TextEditingController ticketTitle =
                              TextEditingController(text: ticket.title);
                          final TextEditingController ticketDescription =
                              TextEditingController(text: ticket.description);
                          final TextEditingController ticketTypeController =
                              TextEditingController(text: ticket.ticketType);
                          final TextEditingController price =
                              TextEditingController(text: "${ticket.price}");
                          final TextEditingController slotsAvailable =
                              TextEditingController(
                                  text: "${ticket.quantity ?? 0}");
                          final groupSizeController = TextEditingController(
                              text: ticket.groupSize.toString());
                          final TextEditingController purchaseStartDate =
                              TextEditingController(
                                  text: ticket.startDate == null
                                      ? ''
                                      : DateFormat('dd/MM/yyyy hh : mm a')
                                          .format(ticket.startDate?.toLocal() ??
                                              DateTime.now()));
                          final TextEditingController purchaseEndDate =
                              TextEditingController(
                                  text: ticket.endDate == null
                                      ? ''
                                      : DateFormat('dd/MM/yyyy hh : mm a')
                                          .format(ticket.endDate?.toLocal() ??
                                              DateTime.now()));
                          controller.slotType.value =
                              ticket.quantity == null || ticket.quantity == 0
                                  ? 1
                                  : 0;

                          return GestureDetector(
                            onTap: () {
                              _selectedIndex.value = index;
                            },
                            child: ValueListenableBuilder(
                                valueListenable: _selectedIndex,
                                builder: (context, selIndex, child) {
                                  return Container(
                                      height: selIndex == index ? null : 108.h,
                                      padding: const EdgeInsets.all(8),
                                      margin: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                          color: isLight.value
                                              ? Colors.white
                                              : Colors.grey[900],
                                          borderRadius:
                                              BorderRadius.circular(15),
                                          border:
                                              Border.all(color: Colors.grey)),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.max,
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceEvenly,
                                        children: [
                                          SizedBox(
                                            height: 8.h,
                                          ),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              if (kDebugMode)
                                                Text('${ticket.id}'),
                                              Expanded(
                                                child: AutoSizeText(
                                                  ticket.title ?? '',
                                                  style: TextStyle(
                                                      fontSize: 16.spMin,
                                                      fontWeight:
                                                          FontWeight.w500),
                                                ),
                                              ),
                                              Text(FormattedCurrency()
                                                  .getFormattedCurrency(
                                                      ticket.price)),
                                              if (selIndex == index)
                                                IconButton(
                                                  icon: const Icon(Icons.close),
                                                  onPressed: () {
                                                    showDialog(
                                                      context: context,
                                                      builder: (BuildContext
                                                          context) {
                                                        return AlertDialog(
                                                          title: const Text(
                                                              'Discard Changes'),
                                                          content: const Text(
                                                              'Are you sure you want to discard changes?'),
                                                          actions: <Widget>[
                                                            TextButton(
                                                              child: const Text(
                                                                  'Cancel'),
                                                              onPressed: () {
                                                                Navigator.of(
                                                                        context)
                                                                    .pop();
                                                              },
                                                            ),
                                                            TextButton(
                                                              child: const Text(
                                                                  'Discard',
                                                                  style: TextStyle(
                                                                      color: Colors
                                                                          .red)),
                                                              onPressed: () {
                                                                Navigator.of(
                                                                        context)
                                                                    .pop();
                                                                _selectedIndex
                                                                        .value =
                                                                    null;
                                                              },
                                                            ),
                                                          ],
                                                        );
                                                      },
                                                    );
                                                  },
                                                ),
                                            ],
                                          ),
                                          if (selIndex != index)
                                            Align(
                                              alignment: Alignment.bottomLeft,
                                              child: AutoSizeText(
                                                '${DateFormat('dd MMM yy').format(ticket.startDate?.toLocal() ?? DateTime.now())} to ${DateFormat('dd MMM yy').format(ticket.endDate?.toLocal() ?? DateTime.now())} - '
                                                '${(() {
                                                  final now = DateTime.now();
                                                  final endDate = ticket.endDate
                                                          ?.toLocal() ??
                                                      DateTime.now();
                                                  final difference =
                                                      endDate.difference(now);

                                                  if (difference.isNegative) {
                                                    return 'expired ${highPrecisiontimeSince(endDate, preffixPastDate: "", suffixPastDate: "ago")}';
                                                  } else if (difference
                                                              .inDays ==
                                                          0 &&
                                                      endDate.year ==
                                                          now.year &&
                                                      endDate.month ==
                                                          now.month &&
                                                      endDate.day == now.day) {
                                                    return 'expires today';
                                                  } else {
                                                    return 'expires in ${highPrecisiontimeSince(endDate, preffixFutureDate: "", suffixFutureDate: "")}';
                                                  }
                                                })()}',
                                                style: TextStyle(
                                                  fontSize: 12.spMin,
                                                  color: Colors.grey,
                                                ),
                                              ),
                                            ),
                                          if (selIndex == index)
                                            Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                SizedBox(height: 12.h),
                                                MyTextFieldwValidator(
                                                  controller: ticketTitle,
                                                  title: 'Title',
                                                ),
                                                MyTextFieldwValidator(
                                                  controller: ticketDescription,
                                                  title: 'Ticket Description',
                                                ),
                                                SizedBox(height: 8.h),
                                                Row(
                                                  children: [
                                                    Expanded(
                                                      child: MyDropdownMenu(
                                                        title: 'Ticket Type',
                                                        onSelected: (val) {
                                                          if (val == "GROUP") {
                                                            group(true);
                                                          } else {
                                                            group(false);
                                                          }
                                                        },
                                                        lists: Get.put(
                                                                GlobalControllers())
                                                            .enums
                                                            .value
                                                            .ticketType,
                                                        controller:
                                                            ticketTypeController,
                                                      ),
                                                    ),
                                                    Obx(() => group.value
                                                        ? Padding(
                                                            padding:
                                                                const EdgeInsets
                                                                    .all(8.0),
                                                            child: Column(
                                                              children: [
                                                                Text(
                                                                  'Group Size',
                                                                  style: TextStyle(
                                                                      fontSize: 14
                                                                          .spMin,
                                                                      fontWeight:
                                                                          FontWeight
                                                                              .w600),
                                                                ),
                                                                SizedBox(
                                                                  width: 120,
                                                                  child:
                                                                      Expanded(
                                                                    child:
                                                                        TextField(
                                                                      keyboardType:
                                                                          TextInputType
                                                                              .number,
                                                                      controller:
                                                                          groupSizeController,
                                                                      decoration: const InputDecoration(
                                                                          label: Text(
                                                                              'Size'),
                                                                          border:
                                                                              OutlineInputBorder()),
                                                                    ),
                                                                  ),
                                                                ),
                                                              ],
                                                            ),
                                                          )
                                                        : const SizedBox())
                                                  ],
                                                ),
                                                SizedBox(height: 8.h),
                                                Text(
                                                  'slots available',
                                                  style: TextStyle(
                                                      fontSize: 14.spMin,
                                                      fontWeight:
                                                          FontWeight.w600),
                                                ),
                                                GetX<EditTicketController>(
                                                    builder:
                                                        (controller) => Row(
                                                              crossAxisAlignment:
                                                                  CrossAxisAlignment
                                                                      .start,
                                                              children: [
                                                                Expanded(
                                                                  child: Column(
                                                                    children: [
                                                                      TextButton.icon(
                                                                          onPressed: () {
                                                                            controller.slotType.value =
                                                                                0; // Update the value directly
                                                                          },
                                                                          label: const Text('Limited slots'),
                                                                          icon: Icon(controller.slotType.value == 0 ? Icons.radio_button_checked : Icons.radio_button_off)),
                                                                      if (controller
                                                                              .slotType
                                                                              .value ==
                                                                          0)
                                                                        MyTextFieldwValidator(
                                                                            keyboardType: TextInputType
                                                                                .number,
                                                                            validator:
                                                                                (value) {
                                                                              if (value == null || value.isEmpty) {
                                                                                return 'Slots is required';
                                                                              }
                                                                              return null;
                                                                            },
                                                                            controller:
                                                                                slotsAvailable,
                                                                            titleStyle:
                                                                                const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                                                                            title: 'Slots Available',
                                                                            hint: 'eg. 100')
                                                                    ],
                                                                  ),
                                                                ),
                                                                Expanded(
                                                                  child: TextButton
                                                                      .icon(
                                                                          onPressed:
                                                                              () {
                                                                            controller.slotType.value =
                                                                                1; // Update the value directly
                                                                          },
                                                                          label: const Text(
                                                                              'Unlimited slots'),
                                                                          icon: Icon(controller.slotType.value == 1
                                                                              ? Icons.radio_button_checked
                                                                              : Icons.radio_button_off)),
                                                                )
                                                              ],
                                                            )),
                                                MyTextFieldwValidator(
                                                    keyboardType:
                                                        TextInputType.number,
                                                    validator: (value) {
                                                      if (value == null ||
                                                          value.isEmpty) {
                                                        return 'Price is required';
                                                      }
                                                      return null;
                                                    },
                                                    controller: price,
                                                    titleStyle: const TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w500),
                                                    title: 'Price',
                                                    hint: '500'),
                                                SizedBox(height: 8.h),
                                                Row(children: [
                                                  Expanded(
                                                      child:
                                                          MyTextFieldwValidator(
                                                              readOnly: true,
                                                              validator:
                                                                  (value) {
                                                                if (value ==
                                                                        null ||
                                                                    value
                                                                        .isEmpty) {
                                                                  return 'Purchase Start Date and Time is required';
                                                                }
                                                                return null;
                                                              },
                                                              controller:
                                                                  purchaseStartDate,
                                                              titleStyle: TextStyle(
                                                                  fontSize:
                                                                      14.spMin,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500),
                                                              iconSuffix:
                                                                  IconButton(
                                                                icon: const Icon(
                                                                    Icons
                                                                        .calendar_month),
                                                                onPressed:
                                                                    () async {
                                                                  DateTime?
                                                                      pickedDateTime =
                                                                      await showDatePicker(
                                                                    context:
                                                                        context,
                                                                    initialDate:
                                                                        DateTime
                                                                            .now(),
                                                                    firstDate:
                                                                        DateTime
                                                                            .now(),
                                                                    lastDate: DateTime
                                                                            .now()
                                                                        .add(const Duration(
                                                                            days:
                                                                                365)),
                                                                  );

                                                                  if (pickedDateTime !=
                                                                      null) {
                                                                    TimeOfDay?
                                                                        pickedTime =
                                                                        await showTimePicker(
                                                                      context:
                                                                          context,
                                                                      initialTime:
                                                                          TimeOfDay
                                                                              .now(),
                                                                    );

                                                                    if (pickedTime !=
                                                                        null) {
                                                                      DateTime
                                                                          finalDateTime =
                                                                          DateTime(
                                                                        pickedDateTime
                                                                            .year,
                                                                        pickedDateTime
                                                                            .month,
                                                                        pickedDateTime
                                                                            .day,
                                                                        pickedTime
                                                                            .hour,
                                                                        pickedTime
                                                                            .minute,
                                                                      );

                                                                      String
                                                                          formattedDateTime =
                                                                          DateFormat('dd/MM/yyyy hh : mm a')
                                                                              .format(finalDateTime);
                                                                      purchaseStartDate
                                                                              .text =
                                                                          formattedDateTime;
                                                                    }
                                                                  }
                                                                },
                                                              ),
                                                              title:
                                                                  'Purchase Start Date and Time',
                                                              hint:
                                                                  '13/2/2024 14:00')),
                                                  SizedBox(width: 18.w),
                                                  Expanded(
                                                      child:
                                                          MyTextFieldwValidator(
                                                    readOnly: true,
                                                    validator: (value) {
                                                      if (value == null ||
                                                          value.isEmpty) {
                                                        return 'Purchase End Date is required';
                                                      }
                                                      return null;
                                                    },
                                                    controller: purchaseEndDate,
                                                    titleStyle: TextStyle(
                                                        fontSize: 14.spMin,
                                                        fontWeight:
                                                            FontWeight.w500),
                                                    iconSuffix: IconButton(
                                                      icon: const Icon(
                                                          Icons.calendar_month),
                                                      onPressed: () async {
                                                        DateTime?
                                                            pickedDateTime =
                                                            await showDatePicker(
                                                          context: context,
                                                          initialDate:
                                                              DateTime.now(),
                                                          firstDate:
                                                              DateTime.now(),
                                                          lastDate: DateTime
                                                                  .now()
                                                              .add(
                                                                  const Duration(
                                                                      days:
                                                                          365)),
                                                        );

                                                        if (pickedDateTime !=
                                                            null) {
                                                          TimeOfDay?
                                                              pickedTime =
                                                              await showTimePicker(
                                                            context: context,
                                                            initialTime:
                                                                TimeOfDay.now(),
                                                          );

                                                          if (pickedTime !=
                                                              null) {
                                                            DateTime
                                                                finalDateTime =
                                                                DateTime(
                                                              pickedDateTime
                                                                  .year,
                                                              pickedDateTime
                                                                  .month,
                                                              pickedDateTime
                                                                  .day,
                                                              pickedTime.hour,
                                                              pickedTime.minute,
                                                            );

                                                            String
                                                                formattedDateTime =
                                                                DateFormat(
                                                                        'dd/MM/yyyy hh : mm a')
                                                                    .format(
                                                                        finalDateTime);
                                                            purchaseEndDate
                                                                    .text =
                                                                formattedDateTime;
                                                          }
                                                        }
                                                      },
                                                    ),
                                                    hint: '13/2/2024 14:00',
                                                    title:
                                                        'Purchase End Date and\nTime',
                                                  )),
                                                ]),
                                                SizedBox(height: 18.h),
                                                Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              8.0),
                                                      child: Obx(
                                                        () => ElevatedButton(
                                                          onPressed: () {
                                                            showDialog(
                                                              context: context,
                                                              builder:
                                                                  (BuildContext
                                                                      context) {
                                                                return AlertDialog(
                                                                  title: const Text(
                                                                      'Delete Ticket'),
                                                                  content:
                                                                      const Text(
                                                                          'Are you sure you want to delete this ticket?'),
                                                                  actions: <Widget>[
                                                                    TextButton(
                                                                      child: const Text(
                                                                          'Cancel'),
                                                                      onPressed:
                                                                          () {
                                                                        Navigator.of(context)
                                                                            .pop();
                                                                      },
                                                                    ),
                                                                    TextButton(
                                                                      child: const Text(
                                                                          'Delete',
                                                                          style:
                                                                              TextStyle(color: Colors.red)),
                                                                      onPressed:
                                                                          () {
                                                                        Navigator.of(context)
                                                                            .pop();
                                                                        controller
                                                                            .deleteTicket(
                                                                              context: context,
                                                                              eventId: widget.eventId,
                                                                              ticket: ticket,
                                                                              description: ticketDescription.text,
                                                                              type: ticketTypeController.text,
                                                                              quantity: slotsAvailable.text.isEmpty || slotsAvailable.text == "" ? 0 : int.parse(slotsAvailable.text.toString()),
                                                                              price: int.parse(price.text),
                                                                              title: ticketDescription.text,
                                                                              startDate: parseCustomDateTime(purchaseStartDate.text),
                                                                              endDate: parseCustomDateTime(purchaseEndDate.text),
                                                                            )
                                                                            .whenComplete(() =>
                                                                                _selectedIndex.value = null);
                                                                      },
                                                                    ),
                                                                  ],
                                                                );
                                                              },
                                                            );
                                                          },
                                                          style: ElevatedButton
                                                              .styleFrom(
                                                            backgroundColor: Colors
                                                                .red, // Set background color to red
                                                          ),
                                                          child: controller
                                                                  .isDeleting
                                                                  .value
                                                              ? const CircularProgressIndicator(
                                                                  color: Colors
                                                                      .white,
                                                                )
                                                              : const Icon(
                                                                  Icons.delete),
                                                        ),
                                                      ),
                                                    ),
                                                    Expanded(
                                                      child: Obx(
                                                        () => MyButton(
                                                          // isExpanded: true,
                                                          showLoading:
                                                              controller
                                                                  .isloading
                                                                  .value,
                                                          label: 'Edit Ticket',
                                                          onClick: () async {
                                                            controller
                                                                .editTicket(
                                                                    context:
                                                                        context,
                                                                    eventId: widget
                                                                        .eventId,
                                                                    ticket:
                                                                        ticket,
                                                                    description:
                                                                        ticketDescription
                                                                            .text,
                                                                    type: ticketTypeController
                                                                        .text,
                                                                    quantity: slotsAvailable.text.isEmpty || slotsAvailable.text == ""
                                                                        ? 0
                                                                        : int.parse(slotsAvailable.text
                                                                            .toString()),
                                                                    price: int.parse(price
                                                                        .text),
                                                                    title: ticketTitle
                                                                        .text,
                                                                    startDate: parseCustomDateTime(
                                                                        purchaseStartDate
                                                                            .text),
                                                                    endDate: parseCustomDateTime(
                                                                        purchaseEndDate
                                                                            .text),
                                                                    groupSize: group.value
                                                                        ? int.parse(groupSizeController.text)
                                                                        : null)
                                                                .whenComplete(() => _selectedIndex.value = null);
                                                          },
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                )
                                              ],
                                            )
                                        ],
                                      ));
                                }),
                          );
                        });
                  }
                  return ListView.builder(
                      itemCount: 3,
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        return Shimmer.fromColors(
                          baseColor: Colors.grey[300]!,
                          highlightColor: Colors.grey[100]!,
                          child: Container(
                            margin: const EdgeInsets.all(4),
                            width: double.infinity,
                            height: 100.h,
                            color: Colors.white,
                          ),
                        );
                      });
                }),
          ),
        ));
  }
}

class AddTicketDialog extends StatefulWidget {
  final int eventId;
  final bool? upload;
  const AddTicketDialog({super.key, required this.eventId, this.upload});

  @override
  State<AddTicketDialog> createState() => _AddTicketDialogState();
}

class _AddTicketDialogState extends State<AddTicketDialog> {
  final controller = Get.put(EditTicketController());
  final ticketTitleController = TextEditingController();
  final TextEditingController ticketDescriptionController =
      TextEditingController();
  final TextEditingController ticketTypeController = TextEditingController();
  final TextEditingController priceController = TextEditingController();
  final TextEditingController slotsAvailableController =
      TextEditingController();
  final TextEditingController purchaseStartDateController =
      TextEditingController();
  final TextEditingController purchaseEndDateController =
      TextEditingController();
  final groupSizeController = TextEditingController();
  final group = false.obs;
  final GlobalControllers globalControllers = Get.find();
  final GlobalKey<FormState> _formKey4 = GlobalKey<FormState>();
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Ticket'),
      content: Form(
        key: _formKey4,
        child: SingleChildScrollView(
            child: Column(
          children: [
            MyTextFieldwValidator(
              controller: ticketTitleController,
              title: 'Title',
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Ticket Description is required';
                }
                return null;
              },
            ),
            MyTextFieldwValidator(
              controller: ticketDescriptionController,
              title: 'Ticket Description',
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Ticket Description is required';
                }
                return null;
              },
            ),
            SizedBox(height: 8.h),
            Row(
              children: [
                Expanded(
                  child: MyDropdownMenu(
                    title: 'Ticket Type',
                    lists: globalControllers.enums.value.ticketType,
                    width: 270.w,
                    onSelected: (val) {
                      if (val == "GROUP") {
                        group(true);
                      } else {
                        group(false);
                      }
                    },
                    controller: ticketTypeController,
                  ),
                ),
                Obx(() => group.value
                    ? Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Column(
                          children: [
                            Text(
                              'Group Size',
                              style: TextStyle(
                                  fontSize: 14.spMin,
                                  fontWeight: FontWeight.w600),
                            ),
                            SizedBox(
                              width: 120,
                              child: Expanded(
                                child: TextField(
                                  controller: groupSizeController,
                                  keyboardType: TextInputType.number,
                                  decoration: const InputDecoration(
                                      label: Text('Size'),
                                      border: OutlineInputBorder()),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    : const SizedBox())
              ],
            ),
            SizedBox(height: 8.h),
            Text(
              'Slots Available',
              style: TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w600),
            ),
            GetX<EditTicketController>(
                builder: (controller) => Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            children: [
                              TextButton.icon(
                                  onPressed: () {
                                    controller.slotType.value =
                                        0; // Update the value directly
                                  },
                                  label: const Text('Limited slots'),
                                  icon: Icon(controller.slotType.value == 0
                                      ? Icons.radio_button_checked
                                      : Icons.radio_button_off)),
                              if (controller.slotType.value == 0)
                                MyTextFieldwValidator(
                                    keyboardType: TextInputType.number,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Slots is required';
                                      }
                                      if (int.tryParse(value) == null ||
                                          int.parse(value) <= 0) {
                                        return 'Slots must be a positive number';
                                      }
                                      return null;
                                    },
                                    controller: slotsAvailableController,
                                    titleStyle: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500),
                                    title: 'Slots Available',
                                    hint: 'e.g. 100')
                            ],
                          ),
                        ),
                        Expanded(
                          child: TextButton.icon(
                              onPressed: () {
                                controller.slotType.value =
                                    1; // Update the value directly
                              },
                              label: const Text('Unlimited slots'),
                              icon: Icon(controller.slotType.value == 1
                                  ? Icons.radio_button_checked
                                  : Icons.radio_button_off)),
                        )
                      ],
                    )),
            MyTextFieldwValidator(
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Price is required';
                  }
                  if (double.tryParse(value) == null ||
                      double.parse(value) < 0) {
                    return 'Price must be a positive number';
                  }
                  return null;
                },
                controller: priceController,
                titleStyle:
                    const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                title: 'Price',
                hint: '500'),
            SizedBox(height: 8.h),
            MyTextFieldwValidator(
                readOnly: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Purchase Start Date and Time is required';
                  }
                  return null;
                },
                controller: purchaseStartDateController,
                titleStyle:
                    TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
                iconSuffix: IconButton(
                  icon: const Icon(Icons.calendar_month),
                  onPressed: () async {
                    DateTime? pickedDateTime = await showDatePicker(
                      context: context,
                      initialDate: DateTime.now(),
                      firstDate: DateTime.now(),
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                    );

                    if (pickedDateTime != null) {
                      TimeOfDay? pickedTime = await showTimePicker(
                        context: context,
                        initialTime: TimeOfDay.now(),
                      );

                      if (pickedTime != null) {
                        DateTime finalDateTime = DateTime(
                          pickedDateTime.year,
                          pickedDateTime.month,
                          pickedDateTime.day,
                          pickedTime.hour,
                          pickedTime.minute,
                        );

                        String formattedDateTime =
                            DateFormat('dd/MM/yyyy hh : mm a')
                                .format(finalDateTime);
                        purchaseStartDateController.text = formattedDateTime;
                      }
                    }
                  },
                ),
                title: 'Purchase Start Date and Time',
                hint: '13/2/2024 2:00 PM'),
            SizedBox(height: 8.h),
            MyTextFieldwValidator(
              readOnly: true,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Purchase End Date is required';
                }
                return null;
              },
              controller: purchaseEndDateController,
              titleStyle:
                  TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
              iconSuffix: IconButton(
                icon: const Icon(Icons.calendar_month),
                onPressed: () async {
                  DateTime? pickedDateTime = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime.now(),
                    lastDate: DateTime.now().add(const Duration(days: 365)),
                  );

                  if (pickedDateTime != null) {
                    TimeOfDay? pickedTime = await showTimePicker(
                      context: context,
                      initialTime: TimeOfDay.now(),
                    );

                    if (pickedTime != null) {
                      DateTime finalDateTime = DateTime(
                        pickedDateTime.year,
                        pickedDateTime.month,
                        pickedDateTime.day,
                        pickedTime.hour,
                        pickedTime.minute,
                      );

                      String formattedDateTime =
                          DateFormat('dd/MM/yyyy HH:mm a')
                              .format(finalDateTime);
                      purchaseEndDateController.text = formattedDateTime;
                    }
                  }
                },
              ),
              hint: '13/2/2024 2:00 PM',
              title: 'Purchase End Date and Time',
            ),
          ],
        )),
      ),
      actions: [
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: SizedBox(
                  height: 45.h,
                  child: const Center(
                      child: Text(
                    'Cancel',
                  )),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Obx(
                () => MyButton(
                  showLoading: controller.isCreatingTicket.value,
                  onClick: () async {
                    if (_formKey4.currentState!.validate()) {
                      await controller.createTickets(
                          context: context,
                          eventId: widget.eventId,
                          tickets: [
                            {
                              "quantity": null,
                              "title": ticketTitleController.text,
                              "price": int.parse(priceController.text),
                              "ticket_type":
                                  ticketTypeController.text.toUpperCase(),
                              "description": ticketDescriptionController.text,
                              "start_date": convertToIso8601(
                                  purchaseStartDateController.text),
                              "end_date": convertToIso8601(
                                  purchaseEndDateController.text),
                              "group_size": group.value
                                  ? int.parse(groupSizeController.text)
                                  : null
                            }
                          ]).whenComplete(() async {
                        await controller.fetchTickets(widget.eventId);
                        if (controller.createTicketstatus.isTrue) {
                          Navigator.pop(context);
                        }
                      });
                    }
                  },
                  label: 'Add',
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
