import 'dart:async';
import 'package:date_time_format/date_time_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/cardPayment.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/confirm_payment_screen.dart';
import 'package:onekitty/screens/widgets/payment_radio.dart';
import 'package:onekitty/utils/common_strings.dart';
import 'package:onekitty/utils/my_quill_editor.dart';
// import '../../../../utils/my_quill_editor.dart';
import '../../../../utils/utils_exports.dart';

// ignore_for_file: must_be_immutable
class ContributingToAKitty extends StatefulWidget {
  const ContributingToAKitty({super.key});

  @override
  State<ContributingToAKitty> createState() => _ContributingToAKittyState();
}

class _ContributingToAKittyState extends State<ContributingToAKitty> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController nameEditTextController = TextEditingController();
  TextEditingController amountEditTextController = TextEditingController();
  TextEditingController phoneNumberController = TextEditingController();
  TextEditingController emailTextController = TextEditingController();
  TextEditingController identifierController = TextEditingController();
  ContributeController contributeController = Get.find();
  late Timer _timer;
  DateTime tagetDate = DateTime.now().add(const Duration(days: 1));

  // Add a value notifier for time-dependent UI elements
  final ValueNotifier<DateTime> _currentTimeNotifier =
      ValueNotifier<DateTime>(DateTime.now());
  getKittyData() async {
    var params = Get.parameters;
    String kittyId = params['id'] ?? "";
    int? id = int.tryParse(kittyId);
    if (id != null) {
      await contributeController.getKitty(id: id);
    } else {
      contributeController.apiMessage.value = "Invalid Kitty ID : $kittyId";
    }
  }

  @override
  void initState() {
    super.initState();
    // Replace the setState timer with a more targeted approach
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _currentTimeNotifier.value = DateTime.now();
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
      getKittyData();
    });
  }

  bool isNameChecked = false;
  bool isNumberChecked = false;

  String? selectedChannel = "M-Pesa";
  @override
  void dispose() {
    _timer.cancel(); // Cancel the timer to prevent memory leaks
    _currentTimeNotifier.dispose(); // Dispose the ValueNotifier
    nameEditTextController.dispose();
    amountEditTextController.dispose();
    phoneNumberController.dispose();
    emailTextController.dispose();
    identifierController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return ScreenUtilInit(
        splitScreenMode: true,
        designSize: const Size(392.72727272727275, 850.9090909090909),
        builder: (context, child) {
          return Scaffold(
            appBar: AppBar(
              // leading: IconButton(
              //   icon: const Icon(Icons.arrow_back, color: Colors.white),
              //   onPressed: () => Navigator.pop(context),
              // ),
              title: Text('Contribute to Kitty',
                  style: textTheme.titleLarge?.copyWith(color: Colors.white)),
              centerTitle: true,
              elevation: 1,
            ),
            body: Form(
              key: _formKey,
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      physics: const BouncingScrollPhysics(),
                      padding: EdgeInsets.symmetric(horizontal: 24.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ColoredBox(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              child: _buildKittyHeader(context)),
                          SizedBox(height: 16.h),
                          _buildKittyInfoCard(context),
                          SizedBox(height: 16.h),
                          _buildContributionForm(context),
                        ],
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 24.w),
                    child: _buildActionButton(context),
                  ),
                ],
              ),
            ),
          );
        });
  }

  Widget _buildStatusChip() {
    final statusColor = _getStatusColor(
        contributeController.kittStatus.value.isEmpty
            ? "Active"
            : contributeController.kittStatus.value);
    return Obx(
      () => Container(
        width: 120.w,
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
            color: statusColor.withOpacity(0.15),
            borderRadius: BorderRadius.circular(30)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 8.w,
              height: 8.w,
              decoration: BoxDecoration(
                color: statusColor,
                shape: BoxShape.circle,
              ),
            ),
            SizedBox(width: 8.w),
            Text(
              contributeController.kittStatus.value.isEmpty
                  ? "Active"
                  : contributeController.kittStatus.value,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w700,
                color: statusColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case "active":
        return const Color(0xFF00C853);
      case "completed":
        return const Color(0xFF6200EA);
      case "settlement initiated":
        return const Color(0xFFFFD600);
      default:
        return const Color(0xFFE53935);
    }
  }

  Widget _buildKittyHeader(BuildContext context) {
    return Column(
      children: [
        Column(
          children: [
            Obx(
              () => contributeController.isgetkittyloading.isTrue
                  ? const Center(child: CircularProgressIndicator())
                  : Text.rich(TextSpan(children: [
                      TextSpan(
                        text: contributeController.kittGoten.value.title ?? '',
                        style:
                            Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                      ),
                    ])),
            ),
            Row(
              children: [
                // Expanded(child: _buildProgressIndicator()),
                const Spacer(),
                SizedBox(
                  width: 120.w,
                  child: Padding(
                    padding: const EdgeInsets.all(4.0),
                    child: _buildStatusChip(),
                  ),
                ),
              ],
            )
          ],
        ),
        SizedBox(height: 8.h),
        Obx(
          () => contributeController.isgetkittyloading.isTrue
              ? const Center(child: SizedBox())
              : QuillEditorWidget(
                  text: extractText(
                      contributeController.kittGoten.value.description ?? ""),
                  tag: 'desc', // Add unique tag
                ),
        ),
      ],
    );
  }

  Widget _buildKittyInfoCard(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          children: [
            _buildInfoRow(
              icon: Icons.calendar_today,
              label: 'Created',
              value: DateFormat('MMM dd, yyyy').format(
                  contributeController.kittGoten.value.createdAt ??
                      DateTime.now()),
            ),
            Divider(height: 16.h),
            _buildInfoRow(
              icon: Icons.people_alt_outlined,
              label: 'Volumes',
              value: contributeController.volumes.toString(),
            ),
            Divider(height: 16.h),
            Row(
              children: [
                Icon(Icons.timer_outlined,
                    size: 18.sp, color: Colors.grey[600]),
                SizedBox(width: 8.w),
                Text(
                  'Ends:',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[800],
                  ),
                ),
                SizedBox(width: 4.w),
                Expanded(
                  child: _buildTimeWidget(
                    contributeController.kittGoten.value.endDate ??
                        DateTime.now(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
      {required IconData icon, required String label, required String value}) {
    return Row(
      children: [
        Icon(icon, size: 18.sp, color: Colors.grey[600]),
        SizedBox(width: 8.w),
        Text(
          '$label:',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: Colors.grey[800],
          ),
        ),
        SizedBox(width: 4.w),
        Expanded(
          child: Text(
            value,
            textAlign: TextAlign.right,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w700,
              color: Theme.of(context).primaryColor,
            ),
          ),
        ),
      ],
    );
  }

  // Add a time-dependent widget that uses ValueListenableBuilder
  Widget _buildTimeWidget(DateTime endDate) {
    return ValueListenableBuilder<DateTime>(
      valueListenable: _currentTimeNotifier,
      builder: (context, currentTime, child) {
        return Text(
          DateTimeFormat.relative(
            endDate,
            levelOfPrecision: 2,
            prependIfBefore: 'Ends In',
            ifNow: "Now",
            appendIfAfter: 'ago',
          ),
          textAlign: TextAlign.right,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w700,
            color: Theme.of(context).primaryColor,
          ),
        );
      },
    );
  }

  Widget _buildContributionForm(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Your Information',
            style: Theme.of(context).textTheme.titleMedium),
        SizedBox(height: 16.h),
        _buildNameInput(),
        SizedBox(height: 16.h),
        _buildPhoneInput(),
        SizedBox(height: 16.h),
        Obx(
          () => (contributeController.singleKitty.value.hasMembership ?? false)
              ? Padding(
                  padding: EdgeInsets.only(bottom: 16.h),
                  child: _buildIdentifierInput(),
                )
              : const SizedBox.shrink(),
        ),
        _buildAmountInput(),
        SizedBox(height: 24.h),
        Text('Payment Method', style: Theme.of(context).textTheme.titleMedium),
        SizedBox(height: 12.h),
        ContributeChannelsBuilder(
          selectedChannel: selectedChannel ?? "",
          onChange: (String? newlySelectedChannel) {
            setState(() => selectedChannel = newlySelectedChannel);
          },
        ),
        if (selectedChannel == 'Visa') _buildEmailInput(),
      ],
    );
  }

  Widget _buildNameInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: nameEditTextController,
                decoration: InputDecoration(
                  labelText: 'Full Name',
                  hintText: 'John Doe',
                  suffixIcon: IconButton(
                    icon: Icon(Icons.info_outline, size: 20.w),
                    onPressed: () => _showTooltip(KtStrings.hideNames),
                  ),
                ),
              ),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.max,
          children: [
            Text('Hide  name in contributions',
                style: Theme.of(context).textTheme.bodySmall),
            Checkbox(
              value: isNameChecked,
              onChanged: (v) => setState(() => isNameChecked = v!),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPhoneInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: phoneNumberController,
                keyboardType: TextInputType.phone,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                decoration: InputDecoration(
                  labelText: 'Phone Number',
                  hintText: '0712 345 678',
                  prefixText: '+254 ',
                  suffixIcon: IconButton(
                    icon: Icon(Icons.info_outline, size: 20.w),
                    onPressed: () => _showTooltip(KtStrings.hideNumber),
                  ),
                ),
                validator: (value) {
                  if (value!.isEmpty) return 'Required';
                  if (value.length < 9) return 'Invalid number';
                  return null;
                },
              ),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          mainAxisSize: MainAxisSize.max,
          children: [
            Text('hide number in contributions',
                style: Theme.of(context).textTheme.bodySmall),
            Checkbox(
              value: isNumberChecked,
              onChanged: (v) => setState(() => isNumberChecked = v!),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildIdentifierInput() {
    // final tooltipKey = GlobalKey<TooltipState>();
    return TextFormField(
      controller: identifierController,
      decoration: InputDecoration(
        labelText: contributeController.singleKitty.value.paymentRefLabel ??
            'Payment Ref',
        hintText:
            'Enter your ${contributeController.singleKitty.value.paymentRefLabel ?? 'Payment Ref'}',
        prefixIcon: const Icon(Icons.person_outline),
        suffixIcon: Tooltip(
            // key: tooltipKey,
            // showDuration: const Duration(seconds: 3),
            triggerMode: TooltipTriggerMode.tap,
            message:
                'Please provide your ${contributeController.singleKitty.value.paymentRefLabel ?? 'Payment Ref'}',
            child: Icon(Icons.info_outline, size: 20.w)),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return '"${contributeController.singleKitty.value.paymentRefLabel ?? 'Payment Ref'}" is required';
        }
        return null;
      },
    );
  }

  Widget _buildAmountInput() {
    return TextFormField(
      controller: amountEditTextController,
      keyboardType: TextInputType.number,
      decoration: InputDecoration(
        labelText: 'Amount (KES)',
        hintText: '5000',
        prefixIcon: Padding(
          padding: EdgeInsets.all(12.w),
          child: Text('KES', style: Theme.of(context).textTheme.bodyLarge),
        ),
      ),
      validator: (value) {
        if (value!.isEmpty) return 'Enter amount';
        if (int.tryParse(value) == null) return 'Invalid amount';
        return null;
      },
    );
  }

  Widget _buildEmailInput() {
    return Padding(
      padding: EdgeInsets.only(top: 16.h),
      child: TextFormField(
        controller: emailTextController,
        keyboardType: TextInputType.emailAddress,
        decoration: const InputDecoration(
          labelText: 'Email Address',
          hintText: '<EMAIL>',
          prefixIcon: Icon(Icons.email_outlined),
        ),
      ),
    );
  }

  Widget _buildActionButton(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 16.h),
        child: Obx(
          () => FilledButton.icon(
            icon: contributeController.isContributeloading.value
                ? SizedBox(
                    width: 24.w,
                    height: 24.w,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                  )
                : const Icon(Icons.attach_money_outlined),
            label: Text(
              contributeController.isContributeloading.value
                  ? 'Processing...'
                  : 'Make Contribution',
            ),
            onPressed: _handleContribution,
            style: FilledButton.styleFrom(
              minimumSize: Size(double.infinity, 56.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showTooltip(String message) {
    final tooltipKey = GlobalKey<TooltipState>();
    final tooltip = Tooltip(
      key: tooltipKey,
      message: message,
      child: Container(),
    );
    final overlay = Overlay.of(context);
    final entry = OverlayEntry(
      builder: (context) => tooltip,
    );
    overlay.insert(entry);
    tooltipKey.currentState?.ensureTooltipVisible();
    Future.delayed(const Duration(seconds: 2), () {
      entry.remove();
    });
  }

  void _handleContribution() async {
    if (_formKey.currentState!.validate()) {
      String fullName = nameEditTextController.text.trim();
      String? firstName;
      String? lastName;
      List<String> names = fullName.split(' ');
      if (names.length == 1) {
        firstName = names[0];
      } else if (names.length >= 2) {
        firstName = names[0];
        // last name should be from zero index onwards
        lastName = names.sublist(1).join(' ');
      }
      //-----------GET CHANNEL--------
      final chan = KittyController().getNetworkCode(
        networkTitle: selectedChannel ?? "",
      );

      //----------TRANSACT-----------

      final kitty = contributeController.kittGoten.value;

      bool res = await contributeController.contribute(
        phoneNumber: phoneNumberController.text,
        firstName: firstName,
        secondName: lastName,
        amount: int.parse(amountEditTextController.text.trim()),
        channel: chan!,
        kittyId: kitty.id!,
        shownames: !isNameChecked,
        shownumber: !isNumberChecked,
        email: emailTextController.text,
        paymentRef:
            contributeController.singleKitty.value.hasMembership ?? false
                ? identifierController.text
                : null,
      );

      if (!mounted) return;
      //----------NAVIGATE USER----------
      if (res) {
        if (chan == 0) {
          ToastUtils.showSuccessToast(
            context,
            contributeController.apiMessageContri.string,
            "success",
          );
          Get.to(() => const ProcessPaymentOtp(isChamaContribute: false));
        }
        if (chan == 63902) {
          ToastUtils.showSuccessToast(
            context,
            contributeController.apiMessageContri.string,
            "success",
          );

          try {
            contributeController.confirmpayLoading.value = true;
            int attempts = 0;
            bool result;
            bool success = false; // Flag to track success

            while (attempts < 5) {
              // Maximum 5 attempts
              contributeController.confirmpayLoading.value = true;

              result = await contributeController.confirmContribution(
                  checkoutId: contributeController
                      .contributeData["checkout_request_id"]);

              await Future.delayed(const Duration(
                  seconds: 5)); // Wait for 5 seconds before next attempt
              attempts++;

              if (result) {
                var status = contributeController.status.value;
                if (status == "SUCCESS") {
                  success = true;
                  break;
                }
                if (status == "FAILED") {
                  success = false;
                  break;
                }
              }
            }
            contributeController.confirmpayLoading.value = false;

            if (success) {
              Get.toNamed(
                  NavRoutes.contrSuccessScreen); // Navigate to success screen
            } else {
              Get.toNamed(NavRoutes.contrErrScreen); // Navigate to error screen
            }
          } catch (e) {}
        }
        if (chan == 55) {
          Get.off(() => const CardPayment(isChamaContribute: false));
          //Get.toNamed(NavRoutes.cardpayment);
        }
      } else {
        ToastUtils.showErrorToast(
          context,
          contributeController.apiMessageContri.string,
          "Error",
        );
      }
    } else {
      ToastUtils.showErrorToast(
        context,
        "Check Your Values",
        "Error",
      );
    }
    _formKey.currentState?.reset();
  }

  /// Navigates back to the previous screen.
  onTapArrowLeft(BuildContext context) {
    Navigator.pop(context);
  }
}
