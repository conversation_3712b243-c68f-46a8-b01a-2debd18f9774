import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/controllers/events/events_controller.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/main.dart' show isLight;

/// Controller for handling KYC actions.
class KYCController extends GetxController {
  final ImagePicker _picker = ImagePicker();
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final Logger _logger = Logger();
  final HttpService apiProvider = Get.find();

  // Observable states
  Rx<File?> frontID = Rx<File?>(null);
  Rx<File?> backID = Rx<File?>(null);
  Rx<File?> selfie = Rx<File?>(null);
  RxBool isUploading = false.obs;
  RxDouble uploadProgress = 0.0.obs;
  RxInt currentStep = 1.obs;
  RxString currentUpload = ''.obs;
  RxString frontName = ''.obs;
  RxString backName = ''.obs;
  RxString selfieName = ''.obs;
  RxString apiMessage = ''.obs;

  // Pre-populated ID number from the local user
  final TextEditingController idNumber = TextEditingController(
    text:
        Get.find<Eventcontroller>().getLocalUser()?.idNumber?.toString() == '0'
            ? null
            : Get.find<Eventcontroller>().getLocalUser()?.idNumber?.toString(),
  );

  // Step management
  final totalSteps = 5.obs;
  RxDouble stepProgress = 0.2.obs;

  @override
  void onClose() {
    idNumber.dispose();
    super.onClose();
  }

  /// Updates progress based on current step
  void updateProgress(int step) {
    currentStep.value = step;
    stepProgress.value = step / totalSteps.value;
  }

  /// Main image capture method
  Future<File?> captureImage(String type) async {
    try {
      if ((type == 'front' || type == 'back') && idNumber.text.trim().isEmpty) {
        throw Exception(
            'Please enter your ID number before capturing the image.');
      }

      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 2048,
        maxHeight: 2048,
      );

      if (image == null) return null;

      final File imageFile = File(image.path);
      switch (type) {
        case 'front':
          frontID.value = imageFile;
          break;
        case 'back':
          backID.value = imageFile;
          break;
        case 'selfie':
          selfie.value = imageFile;
          break;
      }
      return imageFile;
    } catch (e) {
      _logger.e('Capture error: $e');
      Get.snackbar(
        'Capture Failed',
        e.toString().replaceAll('Exception: ', ''),
        snackPosition: SnackPosition.bottom,
      );
      return null;
    }
  }

  /// Upload all images to Firebase Storage
  Future<void> uploadImages() async {
    try {
      isUploading.value = true;
      uploadProgress.value = 0.0;

      // Generate unique filenames with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      frontName.value = 'front_$timestamp.jpg';
      backName.value = 'back_$timestamp.jpg';
      selfieName.value = 'selfie_$timestamp.jpg';

      // Upload images
      currentUpload.value = 'Front ID';
      final frontUrl = await _uploadImage(frontID.value!, frontName.value);

      currentUpload.value = 'Back ID';
      final backUrl = await _uploadImage(backID.value!, backName.value);

      currentUpload.value = 'Selfie';
      final selfieUrl = await _uploadImage(selfie.value!, selfieName.value);

      // Update profile
      currentUpload.value = 'Finalizing';
      await _updateProfileAPI(frontUrl, backUrl, selfieUrl);

      Get.offAll(() => const AlreadySubmittedPage());
    } catch (e) {
      _logger.e('Upload error: $e');
      Get.snackbar(
        'Upload Failed',
        e.toString().replaceAll('Exception: ', ''),
        snackPosition: SnackPosition.bottom,
      );
    } finally {
      isUploading.value = false;
    }
  }

  /// Single image upload handler
  Future<String> _uploadImage(File image, String filename) async {
    try {
      Reference ref;
      if (kDebugMode || HttpService.baseUrl == ApiUrls.BASE_URL_DEV) {
        ref = _storage.ref().child(
              'verificationDebug/${Get.find<Eventcontroller>().getLocalUser()?.phoneNumber ?? ''}/$filename',
            );
      } else {
        ref = _storage.ref().child(
              'verification/${Get.find<Eventcontroller>().getLocalUser()?.phoneNumber ?? ''}/$filename',
            );
      }

      final uploadTask = ref.putFile(image);
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        uploadProgress.value = snapshot.bytesTransferred / snapshot.totalBytes;
      });

      await uploadTask;
      return await ref.getDownloadURL();
    } catch (e) {
      _logger.e('Image upload error: $e');
      throw Exception('Failed to upload $filename: ');
    }
  }

  /// API integration for KYC submission with method selection
  Future<void> _updateProfileAPI(
      String frontUrl, String backUrl, String selfieUrl) async {
    try {
      final int kycStatus =
          Get.find<Eventcontroller>().getLocalUser()?.kycStatus ?? 0;
      final Method method = kycStatus == 2 ? Method.PUT : Method.POST;

      final response = await apiProvider.request(
        url: ApiUrls.updateKYC,
        method: method,
        params: {
          'userId': Get.find<Eventcontroller>().getLocalUser()?.id ?? 0,
          'front': frontUrl,
          'back': backUrl,
          'selfie': selfieUrl,
          'idNumber': idNumber.text,
          'identityType':
              "NATIONAL_ID" //todo to be updated to support various types like passport
        },
      );

      if (response.data["status"] ?? false) {
        Get.snackbar(
          'Success',
          '${response.data['message']}',
          backgroundColor: Colors.green,
        );
      } else {
        throw Exception(response.data['message'] ?? 'KYC submission failed');
      }
    } catch (e) {
      _logger.e('API Error: $e');
      apiMessage.value = "Error, please try again";
      rethrow;
    }
  }

  /// Check if all required documents are captured (including id number)
  bool get allDocumentsReady =>
      frontID.value != null &&
      backID.value != null &&
      selfie.value != null &&
      idNumber.text.isNotEmpty;

  /// Check if all images are captured (regardless of id number)
  bool get allImagesCaptured =>
      frontID.value != null && backID.value != null && selfie.value != null;

  /// Reset controller state
  void resetKYCProcess() {
    frontID.value = null;
    backID.value = null;
    selfie.value = null;
    idNumber.clear();
    currentStep.value = 1;
    uploadProgress.value = 0.0;
    apiMessage.value = '';
  }
}

/// A simple page shown when the user has already submitted their KYC.
class AlreadySubmittedPage extends StatelessWidget {
  const AlreadySubmittedPage({super.key});

  @override
  Widget build(BuildContext context) {
    final bool isLightz = isLight.value;
    final Color backgroundColor = isLightz ? Colors.white : Colors.grey[900]!;
    final Color textColor = isLightz ? Colors.black87 : Colors.white70;

    return Scaffold(
      appBar: AppBar(
        title: const Text('KYC Already Submitted'),
        centerTitle: true,
      ),
      body: Container(
        color: backgroundColor,
        width: double.infinity,
        padding: const EdgeInsets.all(20.0),
        child: Center(
          child: Text(
            'You have already submitted your KYC.\n\nPlease wait for verification.',
            style: TextStyle(
              fontSize: 18,
              color: textColor,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}
